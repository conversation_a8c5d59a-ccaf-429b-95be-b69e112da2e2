using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace KiemtraMST
{
    public partial class SimpleTestForm : Form
    {
        private TextBox taxCodeTextBox;
        private Button searchButton;
        private DataGridView resultsGrid;
        private Label statusLabel;
        private TaxService taxService;

        public SimpleTestForm()
        {
            InitializeComponent();
            taxService = new TaxService();
        }

        private void InitializeComponent()
        {
            this.Text = "Test Tra cứu MST";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Tax code input
            var label = new Label();
            label.Text = "Mã số thuế:";
            label.Location = new Point(20, 20);
            label.Size = new Size(80, 20);

            taxCodeTextBox = new TextBox();
            taxCodeTextBox.Location = new Point(110, 20);
            taxCodeTextBox.Size = new Size(150, 20);
            taxCodeTextBox.Text = "0109153660"; // Test value

            searchButton = new Button();
            searchButton.Text = "Tra cứu";
            searchButton.Location = new Point(270, 20);
            searchButton.Size = new Size(80, 25);
            searchButton.Click += SearchButton_Click;

            // Results grid
            resultsGrid = new DataGridView();
            resultsGrid.Location = new Point(20, 60);
            resultsGrid.Size = new Size(740, 400);
            resultsGrid.AllowUserToAddRows = false;
            resultsGrid.ReadOnly = true;
            resultsGrid.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

            // Status label
            statusLabel = new Label();
            statusLabel.Text = "Sẵn sàng";
            statusLabel.Location = new Point(20, 480);
            statusLabel.Size = new Size(400, 20);

            this.Controls.AddRange(new Control[] {
                label, taxCodeTextBox, searchButton, resultsGrid, statusLabel
            });

            SetupGrid();
        }

        private void SetupGrid()
        {
            resultsGrid.Columns.Clear();
            resultsGrid.AutoGenerateColumns = false;

            resultsGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MaSoThue",
                HeaderText = "Mã số thuế",
                DataPropertyName = "MaSoThue",
                Width = 100
            });

            resultsGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TenCongTy",
                HeaderText = "Tên công ty",
                DataPropertyName = "TenCongTy",
                Width = 200
            });

            resultsGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DiaChi",
                HeaderText = "Địa chỉ",
                DataPropertyName = "DiaChi",
                Width = 150
            });

            resultsGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "NguoiDaiDien",
                HeaderText = "Người đại diện",
                DataPropertyName = "NguoiDaiDien",
                Width = 120
            });

            resultsGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TinhTrang",
                HeaderText = "Tình trạng",
                DataPropertyName = "TinhTrang",
                Width = 150
            });
        }

        private async void SearchButton_Click(object sender, EventArgs e)
        {
            try
            {
                string taxCode = taxCodeTextBox.Text.Trim();
                if (string.IsNullOrEmpty(taxCode))
                {
                    MessageBox.Show("Vui lòng nhập mã số thuế!");
                    return;
                }

                searchButton.Enabled = false;
                statusLabel.Text = "Đang tra cứu...";

                var company = await taxService.GetCompanyInfo(taxCode);
                if (company != null && !string.IsNullOrEmpty(company.TenCongTy))
                {
                    var companies = new List<CompanyInfo> { company };
                    resultsGrid.DataSource = companies;
                    statusLabel.Text = $"Tìm thấy thông tin cho mã số thuế {taxCode}";
                }
                else
                {
                    resultsGrid.DataSource = null;
                    statusLabel.Text = "Không tìm thấy thông tin";
                    MessageBox.Show("Không tìm thấy thông tin cho mã số thuế này!");
                }
            }
            catch (Exception ex)
            {
                statusLabel.Text = "Lỗi tra cứu";
                MessageBox.Show($"Lỗi: {ex.Message}");
            }
            finally
            {
                searchButton.Enabled = true;
            }
        }
    }
}
