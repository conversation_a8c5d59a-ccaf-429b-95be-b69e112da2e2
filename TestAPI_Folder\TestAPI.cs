using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Linq;
using HtmlAgilityPack;

public class TestAPI
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("=== TEST API TRA CỨU MST ===");
        
        string taxCode = "0109153660";
        Console.WriteLine($"Đang test với MST: {taxCode}");
        
        try
        {
            string url = $"https://masothue.com/Search/?q={taxCode}&type=auto&token=&force-search=0";
            Console.WriteLine($"URL: {url}");
            
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            client.DefaultRequestHeaders.Add("Accept-Language", "vi-VN,vi;q=0.9,en;q=0.8");
            
            Console.WriteLine("Đang gửi request...");
            string htmlContent = await client.GetStringAsync(url);
            
            Console.WriteLine($"Nhận được HTML, độ dài: {htmlContent.Length} ký tự");
            
            var htmlDoc = new HtmlDocument();
            htmlDoc.LoadHtml(htmlContent);
            
            // Kiểm tra H1
            var h1Node = htmlDoc.DocumentNode.SelectSingleNode("//h1");
            if (h1Node != null)
            {
                Console.WriteLine($"H1 tìm thấy: '{h1Node.InnerText.Trim()}'");
            }
            else
            {
                Console.WriteLine("Không tìm thấy H1");
            }
            
            // Kiểm tra người đại diện
            var representativeLink = htmlDoc.DocumentNode.SelectSingleNode("//a[contains(@href, 'legalName')]");
            if (representativeLink != null)
            {
                Console.WriteLine($"Người đại diện: '{representativeLink.InnerText.Trim()}'");
            }
            else
            {
                Console.WriteLine("Không tìm thấy người đại diện");
            }
            
            // Hiển thị một phần HTML để debug
            Console.WriteLine("\n=== PHẦN HTML ĐẦU (500 ký tự đầu) ===");
            Console.WriteLine(htmlContent.Substring(0, Math.Min(500, htmlContent.Length)));
            
            Console.WriteLine("\n=== PHẦN HTML CUỐI (500 ký tự cuối) ===");
            if (htmlContent.Length > 500)
            {
                Console.WriteLine(htmlContent.Substring(htmlContent.Length - 500));
            }
            
            // Tìm tất cả text trong body
            var bodyText = htmlDoc.DocumentNode.SelectSingleNode("//body")?.InnerText ?? "";
            var lines = bodyText.Split('\n', '\r')
                .Where(l => !string.IsNullOrWhiteSpace(l))
                .Select(l => l.Trim())
                .Take(20) // Chỉ lấy 20 dòng đầu
                .ToArray();
            
            Console.WriteLine("\n=== 20 DÒNG TEXT ĐẦU TIÊN ===");
            for (int i = 0; i < lines.Length; i++)
            {
                Console.WriteLine($"{i + 1:D2}: {lines[i]}");
            }
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Lỗi: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine("\nNhấn Enter để thoát...");
        Console.ReadLine();
    }
}
