using HtmlAgilityPack;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using System.Text;

public class TestProgram
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("=== TEST CHUONG TRINH TRA CUU MA SO THUE ===");
        Console.WriteLine();
        
        Console.Write("Nhap ma so thue: ");
        var maSoThue = Console.ReadLine();
        
        if (string.IsNullOrWhiteSpace(maSoThue))
        {
            Console.WriteLine("Ma so thue khong duoc de trong!");
            return;
        }
        
        try
        {
            string url = $"https://masothue.com/Search/?q={maSoThue}&type=auto&token=&force-search=0";
            
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            
            Console.WriteLine($"Dang tra cuu: {maSoThue}");
            Console.WriteLine($"URL: {url}");
            
            string htmlContent = await client.GetStringAsync(url);
            var htmlDoc = new HtmlDocument();
            htmlDoc.LoadHtml(htmlContent);
            
            Console.WriteLine("Da tai HTML thanh cong!");
            
            // Lấy tên công ty
            var companyNameNode = htmlDoc.DocumentNode.SelectSingleNode("//h1") ?? 
                                 htmlDoc.DocumentNode.SelectSingleNode("//title");
            string tenCongTy = "";
            string maSoThueKetQua = "";
            
            if (companyNameNode != null)
            {
                var fullTitle = companyNameNode.InnerText.Trim();
                Console.WriteLine($"Title: {fullTitle}");
                
                if (fullTitle.Contains(" - "))
                {
                    var parts = fullTitle.Split(" - ");
                    if (parts.Length > 1)
                    {
                        maSoThueKetQua = parts[0].Trim();
                        tenCongTy = parts[1].Trim();
                    }
                }
                else
                {
                    tenCongTy = fullTitle;
                }
            }
            
            // Lấy địa chỉ
            string diaChi = "";
            var addressNode = htmlDoc.DocumentNode.SelectSingleNode("//text()[contains(., 'Dia chi')]");
            if (addressNode != null)
            {
                var parent = addressNode.ParentNode;
                if (parent != null)
                {
                    var nextSibling = parent.NextSibling;
                    while (nextSibling != null)
                    {
                        var text = nextSibling.InnerText.Trim();
                        if (text.Length > 10 && (text.Contains("Phuong") || text.Contains("Quan") ||
                            text.Contains("Thanh pho") || text.Contains("Tinh") || text.Contains("Huyen") ||
                            text.Contains("Xa") || text.Contains("TP") || text.Contains("So")))
                        {
                            diaChi = text;
                            break;
                        }
                        nextSibling = nextSibling.NextSibling;
                    }
                }
            }
            
            // Lấy người đại diện
            string nguoiDaiDien = "";
            var representativeNode = htmlDoc.DocumentNode.SelectSingleNode("//text()[contains(., 'Nguoi dai dien')]");
            if (representativeNode != null)
            {
                var repLink = representativeNode.ParentNode?.ParentNode?.SelectSingleNode(".//a[contains(@href, 'legalName')]");
                if (repLink != null)
                {
                    nguoiDaiDien = repLink.InnerText.Trim();
                }
            }
            
            // Hiển thị kết quả
            Console.WriteLine();
            Console.WriteLine("=== KET QUA TRA CUU ===");
            Console.WriteLine($"Ma so thue    : {maSoThueKetQua}");
            Console.WriteLine($"Ten cong ty   : {tenCongTy}");
            Console.WriteLine($"Dia chi       : {diaChi}");
            Console.WriteLine($"Nguoi dai dien: {nguoiDaiDien}");
            
            if (string.IsNullOrEmpty(tenCongTy))
            {
                Console.WriteLine();
                Console.WriteLine("Khong tim thay thong tin cong ty!");
                Console.WriteLine("Co the ma so thue khong ton tai hoac website da thay doi cau truc.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Loi: {ex.Message}");
        }
        
        Console.WriteLine();
        Console.WriteLine("Nhan Enter de thoat...");
        Console.ReadLine();
    }
}
