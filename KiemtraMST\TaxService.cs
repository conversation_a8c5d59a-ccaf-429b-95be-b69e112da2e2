using HtmlAgilityPack;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.IO;
using System.Collections.Generic;
using System.Text.Encodings.Web;
using System.Text.Unicode;
using System.Text.RegularExpressions;
using OfficeOpenXml;

namespace KiemtraMST
{
    public class TaxService
    {
        private readonly HttpClient _httpClient;

        public TaxService()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "vi-VN,vi;q=0.9,en;q=0.8");

            // Set EPPlus license
            Environment.SetEnvironmentVariable("EPPlus:ExcelPackage:LicenseContext", "NonCommercial");
        }

        public bool IsValidTaxCode(string taxCode)
        {
            if (string.IsNullOrWhiteSpace(taxCode))
                return false;

            taxCode = taxCode.Trim();

            var patterns = new[]
            {
                @"^\d{10}$",                    // 10 chữ số
                @"^\d{10}-\d{3}$"              // 10 chữ số + dấu gạch + 3 chữ số
            };

            foreach (var pattern in patterns)
            {
                if (Regex.IsMatch(taxCode, pattern))
                    return true;
            }

            return false;
        }

        public async Task<CompanyInfo?> GetCompanyInfo(string taxCode)
        {
            try
            {
                string url = $"https://masothue.com/Search/?q={taxCode}&type=auto&token=&force-search=0";
                
                string htmlContent = await _httpClient.GetStringAsync(url);
                var htmlDoc = new HtmlAgilityPack.HtmlDocument();
                htmlDoc.LoadHtml(htmlContent);
                
                var company = new CompanyInfo();
                
                // Lấy tên công ty từ H1
                var h1Node = htmlDoc.DocumentNode.SelectSingleNode("//h1");
                if (h1Node != null)
                {
                    var fullTitle = h1Node.InnerText.Trim();
                    if (fullTitle.Contains(" - "))
                    {
                        var parts = fullTitle.Split(" - ");
                        if (parts.Length > 1)
                        {
                            company.MaSoThue = parts[0].Trim();
                            company.TenCongTy = parts[1].Trim();
                        }
                    }
                    else
                    {
                        company.TenCongTy = fullTitle;
                        company.MaSoThue = taxCode;
                    }
                }
                else
                {
                    company.MaSoThue = taxCode;
                }
                
                // Lấy người đại diện từ link legalName
                var representativeLink = htmlDoc.DocumentNode.SelectSingleNode("//a[contains(@href, 'legalName')]");
                if (representativeLink != null)
                {
                    company.NguoiDaiDien = representativeLink.InnerText.Trim();
                }
                
                // Lấy các thông tin khác
                var bodyText = htmlDoc.DocumentNode.SelectSingleNode("//body")?.InnerText ?? "";
                var lines = bodyText.Split('\n', '\r')
                    .Where(l => !string.IsNullOrWhiteSpace(l))
                    .Select(l => l.Trim())
                    .ToArray();
                
                ExtractCompanyDetails(company, lines);
                
                return company;
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi tra cứu mã số thuế {taxCode}: {ex.Message}");
            }
        }

        private void ExtractCompanyDetails(CompanyInfo company, string[] lines)
        {
            // Tìm thông tin chính xác
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];
                
                // Địa chỉ
                if (line.Equals("Địa chỉ", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 10 && !nextLine.Contains("Ngày") && !nextLine.Contains("Quản lý") && 
                        !nextLine.Contains("Loại hình") && !nextLine.Contains("Tình trạng"))
                    {
                        company.DiaChi = nextLine;
                    }
                }
                // Ngày hoạt động
                else if (line.Equals("Ngày hoạt động", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 4 && (nextLine.Contains("-") || nextLine.Contains("/")) && 
                        !nextLine.Contains("Quản lý") && !nextLine.Contains("Loại hình"))
                    {
                        company.NgayHoatDong = nextLine;
                    }
                }
                // Quản lý bởi
                else if (line.Equals("Quản lý bởi", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 5 && !nextLine.Contains("Loại hình") && !nextLine.Contains("Tình trạng") &&
                        !nextLine.Contains("Ngày"))
                    {
                        company.QuanLyBoi = nextLine;
                    }
                }
                // Loại hình DN
                else if (line.Equals("Loại hình DN", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 5 && !nextLine.Contains("Tình trạng") && !nextLine.Contains("Ngày") &&
                        !nextLine.Contains("Quản lý"))
                    {
                        company.LoaiHinhDN = nextLine;
                    }
                }
                // Tình trạng
                else if (line.StartsWith("Tình trạng", StringComparison.OrdinalIgnoreCase))
                {
                    var tinhTrangText = line.Replace("Tình trạng", "").Trim();
                    if (!string.IsNullOrEmpty(tinhTrangText) && tinhTrangText.Length > 5)
                    {
                        company.TinhTrang = tinhTrangText;
                    }
                    else if (i + 1 < lines.Length)
                    {
                        var nextLine = lines[i + 1].Trim();
                        if (nextLine.Length > 5 && !nextLine.Contains("Ngày") && !nextLine.Contains("Quản lý") &&
                            !nextLine.Contains("Loại hình"))
                        {
                            company.TinhTrang = nextLine;
                        }
                    }
                }
            }
            
            // Fallback logic
            ApplyFallbackLogic(company, lines);
        }

        private void ApplyFallbackLogic(CompanyInfo company, string[] lines)
        {
            // Fallback cho ngày hoạt động
            if (string.IsNullOrEmpty(company.NgayHoatDong))
            {
                var datePatterns = new[]
                {
                    @"\b\d{1,2}[/-]\d{1,2}[/-]\d{4}\b",
                    @"\b\d{4}-\d{1,2}-\d{1,2}\b",
                    @"\b\d{1,2}/\d{1,2}/\d{4}\b",
                    @"\b\d{4}/\d{1,2}/\d{1,2}\b"
                };
                
                foreach (var line in lines)
                {
                    foreach (var pattern in datePatterns)
                    {
                        var match = Regex.Match(line, pattern);
                        if (match.Success)
                        {
                            var dateStr = match.Value;
                            if (DateTime.TryParse(dateStr, out DateTime parsedDate) && 
                                parsedDate.Year >= 2000 && parsedDate.Year <= DateTime.Now.Year + 1)
                            {
                                company.NgayHoatDong = dateStr;
                                goto DateFound;
                            }
                        }
                    }
                }
                DateFound:;
            }
            
            // Fallback cho loại hình DN
            if (string.IsNullOrEmpty(company.LoaiHinhDN))
            {
                if (!string.IsNullOrEmpty(company.TenCongTy))
                {
                    if (company.TenCongTy.Contains("HỘ KINH DOANH"))
                        company.LoaiHinhDN = "Hộ kinh doanh cá thể";
                    else if (company.TenCongTy.Contains("TNHH"))
                        company.LoaiHinhDN = "Công ty TNHH";
                    else if (company.TenCongTy.Contains("Cổ phần"))
                        company.LoaiHinhDN = "Công ty cổ phần";
                    else if (company.TenCongTy.Contains("Tư nhân"))
                        company.LoaiHinhDN = "Doanh nghiệp tư nhân";
                }
            }
            
            // Fallback cho tình trạng
            if (string.IsNullOrEmpty(company.TinhTrang))
            {
                foreach (var line in lines)
                {
                    if (line.Contains("Đang hoạt động") || line.Contains("Ngừng hoạt động") || 
                        line.Contains("Tạm ngừng") || line.Contains("Giải thể"))
                    {
                        company.TinhTrang = line;
                        break;
                    }
                }
            }
        }

        public async Task<List<string>> ReadTaxCodesFromFile(string filePath)
        {
            var taxCodes = new List<string>();
            var extension = Path.GetExtension(filePath).ToLower();
            
            switch (extension)
            {
                case ".txt":
                    var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
                    foreach (var line in lines)
                    {
                        var taxCode = line.Trim();
                        if (!string.IsNullOrWhiteSpace(taxCode))
                        {
                            taxCodes.Add(taxCode);
                        }
                    }
                    break;
                    
                case ".xls":
                case ".xlsx":
                    try
                    {
                        using var package = new ExcelPackage(new FileInfo(filePath));
                        var worksheet = package.Workbook.Worksheets[0];
                        
                        if (worksheet != null)
                        {
                            var rowCount = worksheet.Dimension?.Rows ?? 0;
                            
                            for (int row = 1; row <= rowCount; row++)
                            {
                                var cellValue = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                                if (!string.IsNullOrWhiteSpace(cellValue))
                                {
                                    taxCodes.Add(cellValue);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Lỗi khi đọc file Excel: {ex.Message}");
                    }
                    break;
                    
                default:
                    throw new Exception("Định dạng file không được hỗ trợ!");
            }
            
            return taxCodes;
        }

        public async Task ExportToExcel(List<CompanyInfo> companies, string fileName)
        {
            // Tạo Excel đơn giản bằng CSV với extension .xlsx
            var csv = new StringBuilder();

            // Header
            csv.AppendLine("STT,Mã số thuế,Tên công ty,Địa chỉ,Người đại diện,Ngày hoạt động,Quản lý bởi,Loại hình DN,Tình trạng");

            // Dữ liệu
            for (int i = 0; i < companies.Count; i++)
            {
                var company = companies[i];
                csv.AppendLine($"{i + 1},\"{company.MaSoThue}\",\"{company.TenCongTy}\",\"{company.DiaChi}\",\"{company.NguoiDaiDien}\",\"{company.NgayHoatDong}\",\"{company.QuanLyBoi}\",\"{company.LoaiHinhDN}\",\"{company.TinhTrang}\"");
            }

            // Lưu với encoding UTF-8 BOM để Excel hiển thị đúng tiếng Việt
            var utf8WithBom = new UTF8Encoding(true);
            await File.WriteAllTextAsync(fileName, csv.ToString(), utf8WithBom);
        }

        public async Task ExportToJSON(List<CompanyInfo> companies, string fileName)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
            };

            var json = JsonSerializer.Serialize(companies, options);
            await File.WriteAllTextAsync(fileName, json, Encoding.UTF8);
        }

        public async Task ExportToCSV(List<CompanyInfo> companies, string fileName)
        {
            var csv = new StringBuilder();
            csv.AppendLine("Ma so thue,Ten cong ty,Dia chi,Nguoi dai dien,Ngay hoat dong,Quan ly boi,Loai hinh DN,Tinh trang");

            foreach (var company in companies)
            {
                csv.AppendLine($"\"{company.MaSoThue}\",\"{company.TenCongTy}\",\"{company.DiaChi}\",\"{company.NguoiDaiDien}\",\"{company.NgayHoatDong}\",\"{company.QuanLyBoi}\",\"{company.LoaiHinhDN}\",\"{company.TinhTrang}\"");
            }

            var utf8WithBom = new UTF8Encoding(true);
            await File.WriteAllTextAsync(fileName, csv.ToString(), utf8WithBom);
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
