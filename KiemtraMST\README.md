# 🏢 Ứng Dụng Tra Cứu Mã Số Thuế Doanh Nghiệp

Ứng dụng Windows Forms để tra cứu thông tin doanh nghiệp theo mã số thuế từ website masothue.com

## ✨ Tính Năng

### 🔍 **Tra Cứu Đơn Lẻ**
- Tra cứu thông tin doanh nghiệp bằng mã số thuế
- Validation mã số thuế (10 hoặc 13 ký tự)
- L<PERSON><PERSON> lịch sử tra cứu gần đây

### 📁 **Tra Cứu Hàng Loạt**
- <PERSON><PERSON><PERSON> danh sách mã số thuế từ file .txt, .xls, .xlsx
- Hiển thị tiến trình tra cứu với progress bar
- Báo cáo kết quả thành công/lỗi

### 📊 **Hiển Thị Kết Quả**
- Bảng kết quả với 8 cột thông tin:
  - <PERSON><PERSON> số thuế
  - Tên công ty
  - Địa chỉ
  - Người đại diện
  - <PERSON><PERSON>y hoạt động
  - Quản lý bởi
  - <PERSON><PERSON><PERSON> hình doanh nghiệp
  - Tình trạng

### 💾 **Xuất Dữ Liệu**
- Xuất toàn bộ hoặc dữ liệu đã chọn
- Hỗ trợ 3 định dạng: Excel (.xlsx), JSON, CSV
- Encoding UTF-8 với BOM cho tiếng Việt

### 📋 **Tiện Ích**
- Copy mã số thuế/tên công ty
- Xóa kết quả tra cứu
- Context menu chuột phải

## 🚀 Cách Sử Dụng

### **1. Tra Cứu Đơn Lẻ**
1. Chọn radio button "Tra cứu mã số thuế đơn lẻ"
2. Nhập mã số thuế (VD: 0109153660)
3. Nhấn "Tra cứu"

### **2. Tra Cứu Hàng Loạt**
1. Chọn radio button "Tra cứu nhiều mã số thuế từ file"
2. Nhấn "Chọn file" và chọn file .txt/.xls/.xlsx
3. Nhấn "Tra cứu"

**Định dạng file:**
- **.txt**: Mỗi dòng một mã số thuế
- **.xls/.xlsx**: Mã số thuế ở cột A, mỗi dòng một mã

### **3. Xuất Kết Quả**
1. Chọn dòng cần xuất (hoặc không chọn để xuất tất cả)
2. Nhấn "Xuất đã chọn" hoặc "Xuất tất cả"
3. Chọn định dạng: Excel, JSON, hoặc CSV

## 📋 Yêu Cầu Hệ Thống

- **OS**: Windows 10/11
- **.NET**: .NET 9.0 Runtime
- **RAM**: 512MB trở lên
- **Disk**: 100MB trống
- **Internet**: Kết nối để tra cứu dữ liệu

## 🛠️ Cài Đặt

### **Cách 1: Chạy Trực Tiếp**
```bash
cd KiemtraMST
.\bin\Debug\net9.0-windows\KiemtraMST.exe
```

### **Cách 2: Build Từ Source**
```bash
cd KiemtraMST
dotnet build
dotnet run
```

### **Cách 3: Publish Standalone**
```bash
cd KiemtraMST
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o ./publish
```

## 📝 Ví Dụ Mã Số Thuế Test

- `0109153660` - CÔNG TY TNHH CÔNG NGHỆ VÀ GIẢI PHÁP PHẦN MỀM DTECH
- `0100109106` - CÔNG TY CỔ PHẦN THƯƠNG MẠI DỊCH VỤ PHONG VŨ
- `0123456789` - Mã số thuế test khác

## 🔧 Cấu Trúc Project

```
KiemtraMST/
├── MainForm.cs          # Giao diện chính
├── TaxService.cs        # Service tra cứu và xuất file
├── CompanyInfo.cs       # Model thông tin công ty
├── Program.cs           # Entry point
├── MainProgram.cs       # Console version
└── test_tax_codes.txt   # File test mã số thuế
```

## 🐛 Xử Lý Lỗi

- **Mã số thuế không hợp lệ**: Kiểm tra định dạng 10 hoặc 13 ký tự
- **Không tìm thấy thông tin**: Mã số thuế có thể không tồn tại
- **Lỗi kết nối**: Kiểm tra internet và thử lại
- **File không đọc được**: Kiểm tra định dạng file và quyền truy cập

## 📞 Hỗ Trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra kết nối internet
2. Thử với mã số thuế khác
3. Restart ứng dụng
4. Kiểm tra file log (nếu có)

## 📄 License

MIT License - Sử dụng tự do cho mục đích cá nhân và thương mại.

---
**Phiên bản**: 1.0.0  
**Ngày cập nhật**: 2025-06-23  
**Tác giả**: Augment Agent
