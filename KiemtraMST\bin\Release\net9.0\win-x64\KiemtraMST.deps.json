{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {}, ".NETCoreApp,Version=v9.0/win-x64": {"KiemtraMST/1.0.0": {"dependencies": {"EPPlus": "8.0.6", "HtmlAgilityPack": "1.12.1", "Microsoft.NET.ILLink.Tasks": "9.0.4", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "9.0.4"}, "runtime": {"KiemtraMST.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.4": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "14.0.425.16305"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "9.0.425.16305"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "9.0.425.16305"}, "clretwrc.dll": {"fileVersion": "9.0.425.16305"}, "clrgc.dll": {"fileVersion": "9.0.425.16305"}, "clrgcexp.dll": {"fileVersion": "9.0.425.16305"}, "clrjit.dll": {"fileVersion": "9.0.425.16305"}, "coreclr.dll": {"fileVersion": "9.0.425.16305"}, "createdump.exe": {"fileVersion": "9.0.425.16305"}, "hostfxr.dll": {"fileVersion": "9.0.425.16305"}, "hostpolicy.dll": {"fileVersion": "9.0.425.16305"}, "mscordaccore.dll": {"fileVersion": "9.0.425.16305"}, "mscordaccore_amd64_amd64_9.0.425.16305.dll": {"fileVersion": "9.0.425.16305"}, "mscordbi.dll": {"fileVersion": "9.0.425.16305"}, "mscorrc.dll": {"fileVersion": "9.0.425.16305"}, "msquic.dll": {"fileVersion": "*******"}}}, "EPPlus/8.0.6": {"dependencies": {"EPPlus.Interfaces": "8.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "System.ComponentModel.Annotations": "5.0.0", "System.Security.Cryptography.Pkcs": "9.0.3", "System.Security.Cryptography.Xml": "9.0.3", "System.Text.Encoding.CodePages": "9.0.3"}, "runtime": {"lib/net9.0/EPPlus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EPPlus.Interfaces/8.0.0": {"runtime": {"lib/net9.0/EPPlus.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "HtmlAgilityPack/1.12.1": {"runtime": {"lib/net8.0/HtmlAgilityPack.dll": {"assemblyVersion": "1.12.1.0", "fileVersion": "1.12.1.0"}}}, "Microsoft.Extensions.Configuration/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.Json/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.3": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileSystemGlobbing": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Primitives/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Microsoft.NET.ILLink.Tasks/9.0.4": {}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Security.Cryptography.Pkcs/9.0.3": {"runtime": {"runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Security.Cryptography.Xml/9.0.3": {"dependencies": {"System.Security.Cryptography.Pkcs": "9.0.3"}, "runtime": {"lib/net9.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Text.Encoding.CodePages/9.0.3": {}}}, "libraries": {"KiemtraMST/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.4": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "EPPlus/8.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7NBGElnvHbs61CgIB2hAS2a8cZ5y0aXXyt7kFh6ujQqrklK2xSXs7RvyQY7Z/IwltZ9y3ZFlaLYtUyVqcnDq2w==", "path": "epplus/8.0.6", "hashPath": "epplus.8.0.6.nupkg.sha512"}, "EPPlus.Interfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-EFr/vUbDYK55sxjfUfLUiv7oiz1f6ZLRYMKILHyfnWS019cYX5zJaQ1U3OojRuED8tgEeXX9QeG7Kj/b0XE7hQ==", "path": "epplus.interfaces/8.0.0", "hashPath": "epplus.interfaces.8.0.0.nupkg.sha512"}, "HtmlAgilityPack/1.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-SP6/2Y26CXtxjXn0Wwsom9Ek35SNWKHEu/IWhNEFejBSSVWWXPRSlpqpBSYWv1SQhYFnwMO01xVbEdK3iRR4hg==", "path": "htmlagilitypack/1.12.1", "hashPath": "htmlagilitypack.1.12.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-RIEeZxWYm77+OWLwgik7DzSVSONjqkmcbuCb1koZdGAV7BgOUWnLz80VMyHZMw3onrVwFCCMHBBdruBPuQTvkg==", "path": "microsoft.extensions.configuration/9.0.3", "hashPath": "microsoft.extensions.configuration.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-q5qlbm6GRUrle2ZZxy9aqS/wWoc+mRD3JeP6rcpiJTh5XcemYkplAcJKq8lU11ZfPom5lfbZZfnQvDqcUhqD5Q==", "path": "microsoft.extensions.configuration.abstractions/9.0.3", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tBNMSDJ2q7WQK2zwPhHY5I/q95t7sf6dT079mGrNm0yOZF/gM9JvR/LtCb/rwhRmh7A6XMnzv5WbpCh9KLq9EQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.3", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mjkp3ZwynNacZk4uq93I0DyCY48FZmi3yRV0xlfeDuWh44KcDunPXHwt8IWr4kL7cVM6eiFVe6YTJg97KzUAUA==", "path": "microsoft.extensions.configuration.json/9.0.3", "hashPath": "microsoft.extensions.configuration.json.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-umczZ3+QPpzlrW/lkvy+IB0p52+qZ5w++aqx2lTCMOaPKzwcbVdrJgiQ3ajw5QWBp7gChLUiCYkSlWUpfjv24g==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.3", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-th2+tQBV5oWjgKhip9GjiIv2AEK3QvfAO3tZcqV3F3dEt5D6Gb411RntCj1+8GS9HaRRSxjSGx/fCrMqIjkb1Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.3", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Rec77KHk4iNpFznHi5/6wF3MlUDcKqg26t8gRYbUm1PSukZ4B6mrXpZsJSNOiwyhhQVkjYbaoZxi5XJgRQ5lFg==", "path": "microsoft.extensions.filesystemglobbing/9.0.3", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-yCCJHvBcRyqapMSNzP+kTc57Eaavq2cr5Tmuil6/XVnipQf5xmskxakSQ1enU6S4+fNg3sJ27WcInV64q24JsA==", "path": "microsoft.extensions.primitives/9.0.3", "hashPath": "microsoft.extensions.primitives.9.0.3.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-xUdlUxiFwXhTYhB4VxKg/IA0+jlZXJPo70LYuMryWbJHdonIpZjw+7DO2B0pWwpXIOs6MlH5WVXPEtfrGEcVZA==", "path": "microsoft.net.illink.tasks/9.0.4", "hashPath": "microsoft.net.illink.tasks.9.0.4.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-4XYeGOYBYtvaF2bH+cps+vPWjI5jQCWR7R1B4dI9I1Da9JQrXYP7mxdci+zKV2Ie4JafaOnOoNaDtzxkaXUxCg==", "path": "system.security.cryptography.pkcs/9.0.3", "hashPath": "system.security.cryptography.pkcs.9.0.3.nupkg.sha512"}, "System.Security.Cryptography.Xml/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-dPG84G5/szhNQ8utXUB4NmrJLGvFe8yd4P0MFjy0/VNxkQNGR6CkkLjptkO5b5rcnw/4c2xDKcrwLxgc1zKWgg==", "path": "system.security.cryptography.xml/9.0.3", "hashPath": "system.security.cryptography.xml.9.0.3.nupkg.sha512"}, "System.Text.Encoding.CodePages/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-80ABiml18jG+6PB6GmrOyMlWla66f4Tp6RgUCJXZLki1Zu+yqbflXvweT86j2xImKHXoVgivto25Z8P5+FO85w==", "path": "system.text.encoding.codepages/9.0.3", "hashPath": "system.text.encoding.codepages.9.0.3.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}