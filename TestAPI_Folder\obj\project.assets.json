{"version": 3, "targets": {"net9.0": {"HtmlAgilityPack/1.11.46": {"type": "package", "compile": {"lib/netstandard2.0/HtmlAgilityPack.dll": {"related": ".deps.json;.pdb;.xml"}}, "runtime": {"lib/netstandard2.0/HtmlAgilityPack.dll": {"related": ".deps.json;.pdb;.xml"}}}}}, "libraries": {"HtmlAgilityPack/1.11.46": {"sha512": "dLMn4EVfJBHWmWK4Uh0XGD76FPLHI0qr2Tm0s1m/xmgiHb1JUb9zB8AzO8HtrkBBlMN6JfCUBYddhqC0hZNR+g==", "type": "package", "path": "htmlagilitypack/1.11.46", "files": [".nupkg.metadata", ".signature.p7s", "htmlagilitypack.1.11.46.nupkg.sha512", "htmlagilitypack.nuspec", "lib/Net35/HtmlAgilityPack.dll", "lib/Net35/HtmlAgilityPack.pdb", "lib/Net35/HtmlAgilityPack.xml", "lib/Net40-client/HtmlAgilityPack.dll", "lib/Net40-client/HtmlAgilityPack.pdb", "lib/Net40-client/HtmlAgilityPack.xml", "lib/Net40/HtmlAgilityPack.XML", "lib/Net40/HtmlAgilityPack.dll", "lib/Net40/HtmlAgilityPack.pdb", "lib/Net45/HtmlAgilityPack.XML", "lib/Net45/HtmlAgilityPack.dll", "lib/Net45/HtmlAgilityPack.pdb", "lib/NetCore45/HtmlAgilityPack.XML", "lib/NetCore45/HtmlAgilityPack.dll", "lib/NetCore45/HtmlAgilityPack.pdb", "lib/netstandard1.3/HtmlAgilityPack.deps.json", "lib/netstandard1.3/HtmlAgilityPack.dll", "lib/netstandard1.3/HtmlAgilityPack.pdb", "lib/netstandard1.3/HtmlAgilityPack.xml", "lib/netstandard1.6/HtmlAgilityPack.deps.json", "lib/netstandard1.6/HtmlAgilityPack.dll", "lib/netstandard1.6/HtmlAgilityPack.pdb", "lib/netstandard1.6/HtmlAgilityPack.xml", "lib/netstandard2.0/HtmlAgilityPack.deps.json", "lib/netstandard2.0/HtmlAgilityPack.dll", "lib/netstandard2.0/HtmlAgilityPack.pdb", "lib/netstandard2.0/HtmlAgilityPack.xml", "lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.XML", "lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.dll", "lib/portable-net45+netcore45+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.pdb", "lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.XML", "lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.dll", "lib/portable-net45+netcore45+wpa81+wp8+MonoAndroid+MonoTouch/HtmlAgilityPack.pdb", "lib/uap10.0/HtmlAgilityPack.XML", "lib/uap10.0/HtmlAgilityPack.dll", "lib/uap10.0/HtmlAgilityPack.pdb", "lib/uap10.0/HtmlAgilityPack.pri"]}}, "projectFileDependencyGroups": {"net9.0": ["HtmlAgilityPack >= 1.11.46"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\AUto\\Kiemtra_MST\\TestAPI_Folder\\TestAPI.csproj", "projectName": "TestAPI", "projectPath": "C:\\Users\\<USER>\\Desktop\\AUto\\Kiemtra_MST\\TestAPI_Folder\\TestAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\AUto\\Kiemtra_MST\\TestAPI_Folder\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"HtmlAgilityPack": {"target": "Package", "version": "[1.11.46, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}