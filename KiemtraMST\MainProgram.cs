using HtmlAgilityPack;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.IO;
using System.Collections.Generic;
using System.Text.Encodings.Web;
using System.Text.Unicode;
using System.Text.RegularExpressions;

// Class để lưu thông tin công ty
public class CompanyInfo
{
    public string TenCongTy { get; set; } = "";
    public string MaSoThue { get; set; } = "";
    public string DiaChi { get; set; } = "";
    public string NguoiDaiDien { get; set; } = "";
    public string NgayHoatDong { get; set; } = "";
    public string QuanLyBoi { get; set; } = "";
    public string LoaiHinhDN { get; set; } = "";
    public string TinhTrang { get; set; } = "";
}

public class MainProgram
{
    public static async Task Main(string[] args)
    {
        try
        {
            Console.OutputEncoding = Encoding.UTF8;
            Console.InputEncoding = Encoding.UTF8;
        }
        catch { }
        
        Console.WriteLine("=== CHUONG TRINH TRA CUU MA SO THUE ===");
        Console.WriteLine();
        
        Console.WriteLine("Chon chuc nang:");
        Console.WriteLine("1. Tra cuu theo ma so thue");
        Console.WriteLine("2. Tra cuu theo ten cong ty");
        Console.WriteLine("3. Tra cuu nhieu ma so thue");
        Console.WriteLine("0. Thoat");
        Console.Write("Nhap lua chon: ");
        
        var choice = Console.ReadLine();
        
        switch (choice)
        {
            case "1":
                await TraCuuTheoMaSoThue();
                break;
            case "2":
                Console.WriteLine("Chuc nang dang phat trien...");
                break;
            case "3":
                Console.WriteLine("Chuc nang dang phat trien...");
                break;
            case "0":
                return;
            default:
                Console.WriteLine("Lua chon khong hop le!");
                break;
        }
    }
    
    static async Task TraCuuTheoMaSoThue()
    {
        Console.Write("Nhap ma so thue: ");
        var maSoThue = Console.ReadLine();
        
        if (string.IsNullOrWhiteSpace(maSoThue))
        {
            Console.WriteLine("Ma so thue khong duoc de trong!");
            return;
        }
        
        var company = await LayThongTinCongTy(maSoThue);
        
        if (company != null && !string.IsNullOrEmpty(company.TenCongTy))
        {
            var companies = new List<CompanyInfo> { company };
            HienThiBang(companies);
            await XuatKetQua(companies);
        }
        else
        {
            Console.WriteLine("Khong tim thay thong tin cho ma so thue nay!");
        }
    }
    
    static async Task<CompanyInfo?> LayThongTinCongTy(string maSoThue)
    {
        try
        {
            string url = $"https://masothue.com/Search/?q={maSoThue}&type=auto&token=&force-search=0";
            
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            client.DefaultRequestHeaders.Add("Accept-Language", "vi-VN,vi;q=0.9,en;q=0.8");
            
            string htmlContent = await client.GetStringAsync(url);
            var htmlDoc = new HtmlDocument();
            htmlDoc.LoadHtml(htmlContent);
            
            var company = new CompanyInfo();
            
            // Lấy tên công ty từ H1
            var h1Node = htmlDoc.DocumentNode.SelectSingleNode("//h1");
            if (h1Node != null)
            {
                var fullTitle = h1Node.InnerText.Trim();
                if (fullTitle.Contains(" - "))
                {
                    var parts = fullTitle.Split(" - ");
                    if (parts.Length > 1)
                    {
                        company.MaSoThue = parts[0].Trim();
                        company.TenCongTy = parts[1].Trim();
                    }
                }
                else
                {
                    company.TenCongTy = fullTitle;
                    company.MaSoThue = maSoThue;
                }
            }
            else
            {
                company.MaSoThue = maSoThue;
            }
            
            // Lấy người đại diện từ link legalName
            var representativeLink = htmlDoc.DocumentNode.SelectSingleNode("//a[contains(@href, 'legalName')]");
            if (representativeLink != null)
            {
                company.NguoiDaiDien = representativeLink.InnerText.Trim();
            }
            
            // Lấy các thông tin khác bằng cách tìm trong toàn bộ text - cải tiến
            var bodyText = htmlDoc.DocumentNode.SelectSingleNode("//body")?.InnerText ?? "";
            var lines = bodyText.Split('\n', '\r')
                .Where(l => !string.IsNullOrWhiteSpace(l))
                .Select(l => l.Trim())
                .ToArray();

            // Tìm thông tin chính xác hơn
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];

                // Địa chỉ
                if (line.Equals("Địa chỉ", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 10 && !nextLine.Contains("Ngày") && !nextLine.Contains("Quản lý") &&
                        !nextLine.Contains("Loại hình") && !nextLine.Contains("Tình trạng"))
                    {
                        company.DiaChi = nextLine;
                    }
                }
                // Ngày hoạt động
                else if (line.Equals("Ngày hoạt động", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 4 && (nextLine.Contains("-") || nextLine.Contains("/")) &&
                        !nextLine.Contains("Quản lý") && !nextLine.Contains("Loại hình"))
                    {
                        company.NgayHoatDong = nextLine;
                    }
                }
                // Quản lý bởi
                else if (line.Equals("Quản lý bởi", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 5 && !nextLine.Contains("Loại hình") && !nextLine.Contains("Tình trạng") &&
                        !nextLine.Contains("Ngày"))
                    {
                        company.QuanLyBoi = nextLine;
                    }
                }
                // Loại hình DN
                else if (line.Equals("Loại hình DN", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 5 && !nextLine.Contains("Tình trạng") && !nextLine.Contains("Ngày") &&
                        !nextLine.Contains("Quản lý"))
                    {
                        company.LoaiHinhDN = nextLine;
                    }
                }
                // Tình trạng
                else if (line.Equals("Tình trạng", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 5 && !nextLine.Contains("Ngày") && !nextLine.Contains("Quản lý") &&
                        !nextLine.Contains("Loại hình"))
                    {
                        company.TinhTrang = nextLine;
                    }
                }
            }

            // Fallback: nếu vẫn thiếu thông tin, thử tìm theo pattern khác
            if (string.IsNullOrEmpty(company.NgayHoatDong))
            {
                // Tìm ngày theo nhiều format khác nhau với Regex
                var datePatterns = new[]
                {
                    @"\b\d{1,2}[/-]\d{1,2}[/-]\d{4}\b",     // dd/mm/yyyy hoặc dd-mm-yyyy
                    @"\b\d{4}-\d{1,2}-\d{1,2}\b",           // yyyy-mm-dd
                    @"\b\d{1,2}/\d{1,2}/\d{4}\b",          // d/m/yyyy
                    @"\b\d{4}/\d{1,2}/\d{1,2}\b"           // yyyy/mm/dd
                };

                foreach (var line in lines)
                {
                    foreach (var pattern in datePatterns)
                    {
                        var match = Regex.Match(line, pattern);
                        if (match.Success)
                        {
                            var dateStr = match.Value;
                            // Kiểm tra xem có phải là ngày hợp lệ không
                            if (DateTime.TryParse(dateStr, out DateTime parsedDate) &&
                                parsedDate.Year >= 2000 && parsedDate.Year <= DateTime.Now.Year + 1)
                            {
                                company.NgayHoatDong = dateStr;
                                goto DateFound;
                            }
                        }
                    }
                }

                // Nếu vẫn không tìm thấy, thử tìm trong context của "Ngày hoạt động"
                for (int i = 0; i < lines.Length; i++)
                {
                    if (lines[i].Contains("Ngày hoạt động", StringComparison.OrdinalIgnoreCase))
                    {
                        // Tìm trong 3 dòng tiếp theo
                        for (int j = i + 1; j <= Math.Min(i + 3, lines.Length - 1); j++)
                        {
                            var nextLine = lines[j].Trim();
                            foreach (var pattern in datePatterns)
                            {
                                var match = Regex.Match(nextLine, pattern);
                                if (match.Success)
                                {
                                    var dateStr = match.Value;
                                    if (DateTime.TryParse(dateStr, out DateTime parsedDate) &&
                                        parsedDate.Year >= 2000 && parsedDate.Year <= DateTime.Now.Year + 1)
                                    {
                                        company.NgayHoatDong = dateStr;
                                        goto DateFound;
                                    }
                                }
                            }
                        }
                    }
                }

                DateFound:;
            }

            if (string.IsNullOrEmpty(company.LoaiHinhDN))
            {
                // Ưu tiên thông tin từ tên công ty trước (chính xác hơn)
                if (!string.IsNullOrEmpty(company.TenCongTy))
                {
                    if (company.TenCongTy.Contains("HỘ KINH DOANH"))
                        company.LoaiHinhDN = "Hộ kinh doanh cá thể";
                    else if (company.TenCongTy.Contains("TNHH"))
                        company.LoaiHinhDN = "Công ty TNHH";
                    else if (company.TenCongTy.Contains("Cổ phần"))
                        company.LoaiHinhDN = "Công ty cổ phần";
                    else if (company.TenCongTy.Contains("Tư nhân"))
                        company.LoaiHinhDN = "Doanh nghiệp tư nhân";
                }

                // Nếu vẫn không có, tìm trong HTML
                if (string.IsNullOrEmpty(company.LoaiHinhDN))
                {
                    foreach (var line in lines)
                    {
                        if (line.Contains("Hộ kinh doanh cá thể") ||
                            line.Contains("Doanh nghiệp tư nhân") ||
                            line.Contains("Công ty cổ phần") ||
                            (line.Contains("TNHH") && !line.Contains("1 thành viên") && !line.Contains("2 thành viên")))
                        {
                            company.LoaiHinhDN = line;
                            break;
                        }
                    }
                }
            }

            if (string.IsNullOrEmpty(company.TinhTrang))
            {
                foreach (var line in lines)
                {
                    if (line.Contains("Đang hoạt động") || line.Contains("Ngừng hoạt động") ||
                        line.Contains("Tạm ngừng") || line.Contains("Giải thể"))
                    {
                        company.TinhTrang = line;
                        break;
                    }
                }
            }
            
            return company;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Loi khi tra cuu ma so thue {maSoThue}: {ex.Message}");
            return null;
        }
    }
    
    static void HienThiBang(List<CompanyInfo> companies)
    {
        if (companies.Count == 0) return;
        
        Console.WriteLine();
        Console.WriteLine("=".PadRight(150, '='));
        Console.WriteLine("KET QUA TRA CUU MA SO THUE");
        Console.WriteLine("=".PadRight(150, '='));
        
        Console.WriteLine($"{"STT",-4} {"Ma so thue",-15} {"Ten cong ty",-40} {"Nguoi dai dien",-25} {"Tinh trang",-20} {"Ngay hoat dong",-15}");
        Console.WriteLine("-".PadRight(150, '-'));
        
        for (int i = 0; i < companies.Count; i++)
        {
            var company = companies[i];
            Console.WriteLine($"{i + 1,-4} {TruncateString(company.MaSoThue, 15),-15} {TruncateString(company.TenCongTy, 40),-40} {TruncateString(company.NguoiDaiDien, 25),-25} {TruncateString(company.TinhTrang, 20),-20} {TruncateString(company.NgayHoatDong, 15),-15}");
        }
        
        Console.WriteLine("-".PadRight(150, '-'));
        Console.WriteLine($"Tong cong: {companies.Count} ket qua");
        Console.WriteLine();
        
        for (int i = 0; i < companies.Count; i++)
        {
            var company = companies[i];
            Console.WriteLine($"=== CHI TIET CONG TY {i + 1} ===");
            Console.WriteLine($"Ma so thue    : {company.MaSoThue}");
            Console.WriteLine($"Ten cong ty   : {company.TenCongTy}");
            Console.WriteLine($"Dia chi       : {company.DiaChi}");
            Console.WriteLine($"Nguoi dai dien: {company.NguoiDaiDien}");
            Console.WriteLine($"Ngay hoat dong: {company.NgayHoatDong}");
            Console.WriteLine($"Quan ly boi   : {company.QuanLyBoi}");
            Console.WriteLine($"Loai hinh DN  : {company.LoaiHinhDN}");
            Console.WriteLine($"Tinh trang    : {company.TinhTrang}");
            Console.WriteLine();
        }
    }
    
    static string TruncateString(string input, int maxLength)
    {
        if (string.IsNullOrEmpty(input)) return "";
        if (input.Length <= maxLength) return input;
        return input.Substring(0, maxLength - 3) + "...";
    }
    
    static async Task XuatKetQua(List<CompanyInfo> companies)
    {
        if (companies.Count == 0) return;
        
        Console.WriteLine("Ban co muon xuat ket qua ra file khong? (y/n): ");
        var choice = Console.ReadLine()?.ToLower();
        
        if (choice == "y" || choice == "yes")
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            
            await XuatCSV(companies, $"KetQua_MST_{timestamp}.csv");
            await XuatJSON(companies, $"KetQua_MST_{timestamp}.json");
            
            Console.WriteLine($"Da xuat file thanh cong:");
            Console.WriteLine($"- CSV: KetQua_MST_{timestamp}.csv");
            Console.WriteLine($"- JSON: KetQua_MST_{timestamp}.json");
        }
    }
    
    static async Task XuatCSV(List<CompanyInfo> companies, string fileName)
    {
        var csv = new StringBuilder();
        csv.AppendLine("Ma so thue,Ten cong ty,Dia chi,Nguoi dai dien,Ngay hoat dong,Quan ly boi,Loai hinh DN,Tinh trang");
        
        foreach (var company in companies)
        {
            csv.AppendLine($"\"{company.MaSoThue}\",\"{company.TenCongTy}\",\"{company.DiaChi}\",\"{company.NguoiDaiDien}\",\"{company.NgayHoatDong}\",\"{company.QuanLyBoi}\",\"{company.LoaiHinhDN}\",\"{company.TinhTrang}\"");
        }
        
        var utf8WithBom = new UTF8Encoding(true);
        await File.WriteAllTextAsync(fileName, csv.ToString(), utf8WithBom);
    }
    
    static async Task XuatJSON(List<CompanyInfo> companies, string fileName)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
        };
        
        var json = JsonSerializer.Serialize(companies, options);
        await File.WriteAllTextAsync(fileName, json, Encoding.UTF8);
    }
}
