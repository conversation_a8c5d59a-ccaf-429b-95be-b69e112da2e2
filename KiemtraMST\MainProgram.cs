using HtmlAgilityPack;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.IO;
using System.Collections.Generic;
using System.Text.Encodings.Web;
using System.Text.Unicode;
using System.Text.RegularExpressions;
using OfficeOpenXml;
using OfficeOpenXml.Style;

// Class để lưu thông tin công ty
public class CompanyInfo
{
    public string TenCongTy { get; set; } = "";
    public string MaSoThue { get; set; } = "";
    public string DiaChi { get; set; } = "";
    public string NguoiDaiDien { get; set; } = "";
    public string NgayHoatDong { get; set; } = "";
    public string QuanLyBoi { get; set; } = "";
    public string LoaiHinhDN { get; set; } = "";
    public string TinhTrang { get; set; } = "";
}

public class MainProgram
{
    public static async Task Main(string[] args)
    {
        try
        {
            Console.OutputEncoding = Encoding.UTF8;
            Console.InputEncoding = Encoding.UTF8;
        }
        catch { }

        // Thiết lập EPPlus license cho version 8+
        Environment.SetEnvironmentVariable("EPPlus:ExcelPackage:LicenseContext", "NonCommercial");

        Console.WriteLine("=== CHUONG TRINH TRA CUU MA SO THUE ===");
        Console.WriteLine();

        Console.WriteLine("Chon chuc nang:");
        Console.WriteLine("1. Tra cuu theo ma so thue");
        Console.WriteLine("2. Tra cuu theo ten cong ty");
        Console.WriteLine("3. Tra cuu nhieu ma so thue tu file");
        Console.WriteLine("0. Thoat");
        Console.Write("Nhap lua chon: ");

        var choice = Console.ReadLine();

        switch (choice)
        {
            case "1":
                await TraCuuTheoMaSoThue();
                break;
            case "2":
                Console.WriteLine("Chuc nang dang phat trien...");
                break;
            case "3":
                await TraCuuNhieuMaSoThue();
                break;
            case "0":
                return;
            default:
                Console.WriteLine("Lua chon khong hop le!");
                break;
        }
    }
    
    static async Task TraCuuTheoMaSoThue()
    {
        Console.Write("Nhap ma so thue: ");
        var maSoThue = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(maSoThue))
        {
            Console.WriteLine("Ma so thue khong duoc de trong!");
            return;
        }

        // Kiểm tra tính hợp lệ của mã số thuế
        if (!KiemTraMaSoThueHopLe(maSoThue))
        {
            Console.WriteLine("Ma so thue khong hop le!");
            Console.WriteLine("Ma so thue phai co 10 hoac 13 chu so (bao gom dau gach ngang)");
            Console.WriteLine("Vi du: 0123456789 hoac 0123456789-001");
            return;
        }

        var company = await LayThongTinCongTy(maSoThue);

        if (company != null && !string.IsNullOrEmpty(company.TenCongTy))
        {
            var companies = new List<CompanyInfo> { company };
            HienThiBang(companies);
            await XuatKetQua(companies);
        }
        else
        {
            Console.WriteLine("Khong tim thay thong tin cho ma so thue nay!");
        }
    }

    static async Task TraCuuNhieuMaSoThue()
    {
        Console.WriteLine("=== TRA CUU NHIEU MA SO THUE TU FILE ===");
        Console.WriteLine();
        Console.WriteLine("Cac dinh dang file ho tro: .txt, .xls, .xlsx");
        Console.WriteLine("Cac ma so thue phai duoc sap xep tung dong");
        Console.WriteLine();
        Console.Write("Nhap duong dan file: ");

        var filePath = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(filePath))
        {
            Console.WriteLine("Duong dan file khong duoc de trong!");
            return;
        }

        if (!File.Exists(filePath))
        {
            Console.WriteLine("File khong ton tai!");
            return;
        }

        var maSoThueList = new List<string>();

        try
        {
            // Đọc file theo extension
            var extension = Path.GetExtension(filePath).ToLower();

            switch (extension)
            {
                case ".txt":
                    maSoThueList = await DocFileTxt(filePath);
                    break;
                case ".xls":
                case ".xlsx":
                    maSoThueList = await DocFileExcel(filePath);
                    break;
                default:
                    Console.WriteLine("Dinh dang file khong duoc ho tro!");
                    return;
            }

            if (maSoThueList.Count == 0)
            {
                Console.WriteLine("Khong tim thay ma so thue nao trong file!");
                return;
            }

            Console.WriteLine($"Tim thay {maSoThueList.Count} ma so thue trong file.");
            Console.WriteLine("Bat dau tra cuu...");
            Console.WriteLine();

            var companies = new List<CompanyInfo>();
            var soLuongThanhCong = 0;
            var soLuongLoi = 0;

            for (int i = 0; i < maSoThueList.Count; i++)
            {
                var maSoThue = maSoThueList[i];
                Console.Write($"[{i + 1}/{maSoThueList.Count}] Tra cuu {maSoThue}... ");

                if (!KiemTraMaSoThueHopLe(maSoThue))
                {
                    Console.WriteLine("KHONG HOP LE");
                    soLuongLoi++;
                    continue;
                }

                try
                {
                    var company = await LayThongTinCongTy(maSoThue);
                    if (company != null && !string.IsNullOrEmpty(company.TenCongTy))
                    {
                        companies.Add(company);
                        Console.WriteLine("THANH CONG");
                        soLuongThanhCong++;
                    }
                    else
                    {
                        Console.WriteLine("KHONG TIM THAY");
                        soLuongLoi++;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"LOI: {ex.Message}");
                    soLuongLoi++;
                }

                // Delay để tránh spam server
                await Task.Delay(1000);
            }

            Console.WriteLine();
            Console.WriteLine($"=== KET QUA ===");
            Console.WriteLine($"Thanh cong: {soLuongThanhCong}");
            Console.WriteLine($"Loi: {soLuongLoi}");
            Console.WriteLine($"Tong cong: {maSoThueList.Count}");
            Console.WriteLine();

            if (companies.Count > 0)
            {
                HienThiBang(companies);
                await XuatKetQuaNhieu(companies);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Loi khi doc file: {ex.Message}");
        }
    }

    static bool KiemTraMaSoThueHopLe(string maSoThue)
    {
        if (string.IsNullOrWhiteSpace(maSoThue))
            return false;

        // Loại bỏ khoảng trắng
        maSoThue = maSoThue.Trim();

        // Pattern cho mã số thuế Việt Nam:
        // - 10 chữ số: 0123456789
        // - 13 ký tự với dấu gạch ngang: 0123456789-001
        var patterns = new[]
        {
            @"^\d{10}$",                    // 10 chữ số
            @"^\d{10}-\d{3}$"              // 10 chữ số + dấu gạch + 3 chữ số
        };

        foreach (var pattern in patterns)
        {
            if (Regex.IsMatch(maSoThue, pattern))
                return true;
        }

        return false;
    }
    
    static async Task<CompanyInfo?> LayThongTinCongTy(string maSoThue)
    {
        try
        {
            string url = $"https://masothue.com/Search/?q={maSoThue}&type=auto&token=&force-search=0";
            
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            client.DefaultRequestHeaders.Add("Accept-Language", "vi-VN,vi;q=0.9,en;q=0.8");
            
            string htmlContent = await client.GetStringAsync(url);
            var htmlDoc = new HtmlDocument();
            htmlDoc.LoadHtml(htmlContent);
            
            var company = new CompanyInfo();
            
            // Lấy tên công ty từ H1
            var h1Node = htmlDoc.DocumentNode.SelectSingleNode("//h1");
            if (h1Node != null)
            {
                var fullTitle = h1Node.InnerText.Trim();
                if (fullTitle.Contains(" - "))
                {
                    var parts = fullTitle.Split(" - ");
                    if (parts.Length > 1)
                    {
                        company.MaSoThue = parts[0].Trim();
                        company.TenCongTy = parts[1].Trim();
                    }
                }
                else
                {
                    company.TenCongTy = fullTitle;
                    company.MaSoThue = maSoThue;
                }
            }
            else
            {
                company.MaSoThue = maSoThue;
            }
            
            // Lấy người đại diện từ link legalName
            var representativeLink = htmlDoc.DocumentNode.SelectSingleNode("//a[contains(@href, 'legalName')]");
            if (representativeLink != null)
            {
                company.NguoiDaiDien = representativeLink.InnerText.Trim();
            }
            
            // Lấy các thông tin khác bằng cách tìm trong toàn bộ text - cải tiến
            var bodyText = htmlDoc.DocumentNode.SelectSingleNode("//body")?.InnerText ?? "";
            var lines = bodyText.Split('\n', '\r')
                .Where(l => !string.IsNullOrWhiteSpace(l))
                .Select(l => l.Trim())
                .ToArray();
            
            // Tìm thông tin chính xác hơn
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];
                
                // Địa chỉ
                if (line.Equals("Địa chỉ", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 10 && !nextLine.Contains("Ngày") && !nextLine.Contains("Quản lý") && 
                        !nextLine.Contains("Loại hình") && !nextLine.Contains("Tình trạng"))
                    {
                        company.DiaChi = nextLine;
                    }
                }
                // Ngày hoạt động
                else if (line.Equals("Ngày hoạt động", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 4 && (nextLine.Contains("-") || nextLine.Contains("/")) && 
                        !nextLine.Contains("Quản lý") && !nextLine.Contains("Loại hình"))
                    {
                        company.NgayHoatDong = nextLine;
                    }
                }
                // Quản lý bởi
                else if (line.Equals("Quản lý bởi", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 5 && !nextLine.Contains("Loại hình") && !nextLine.Contains("Tình trạng") &&
                        !nextLine.Contains("Ngày"))
                    {
                        company.QuanLyBoi = nextLine;
                    }
                }
                // Loại hình DN
                else if (line.Equals("Loại hình DN", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                {
                    var nextLine = lines[i + 1].Trim();
                    if (nextLine.Length > 5 && !nextLine.Contains("Tình trạng") && !nextLine.Contains("Ngày") &&
                        !nextLine.Contains("Quản lý"))
                    {
                        company.LoaiHinhDN = nextLine;
                    }
                }
                // Tình trạng - SỬA LẠI LOGIC ĐỂ LẤY ĐÚNG
                else if (line.StartsWith("Tình trạng", StringComparison.OrdinalIgnoreCase))
                {
                    // Tìm tình trạng trong cùng dòng (format: "Tình trạngĐang hoạt động...")
                    var tinhTrangText = line.Replace("Tình trạng", "").Trim();
                    if (!string.IsNullOrEmpty(tinhTrangText) && tinhTrangText.Length > 5)
                    {
                        company.TinhTrang = tinhTrangText;
                    }
                    // Nếu không có trong cùng dòng, lấy dòng tiếp theo
                    else if (i + 1 < lines.Length)
                    {
                        var nextLine = lines[i + 1].Trim();
                        if (nextLine.Length > 5 && !nextLine.Contains("Ngày") && !nextLine.Contains("Quản lý") &&
                            !nextLine.Contains("Loại hình"))
                        {
                            company.TinhTrang = nextLine;
                        }
                    }
                }
            }

            // Fallback: nếu vẫn thiếu thông tin, thử tìm theo pattern khác
            if (string.IsNullOrEmpty(company.NgayHoatDong))
            {
                // Tìm ngày theo nhiều format khác nhau với Regex
                var datePatterns = new[]
                {
                    @"\b\d{1,2}[/-]\d{1,2}[/-]\d{4}\b",     // dd/mm/yyyy hoặc dd-mm-yyyy
                    @"\b\d{4}-\d{1,2}-\d{1,2}\b",           // yyyy-mm-dd
                    @"\b\d{1,2}/\d{1,2}/\d{4}\b",          // d/m/yyyy
                    @"\b\d{4}/\d{1,2}/\d{1,2}\b"           // yyyy/mm/dd
                };

                foreach (var line in lines)
                {
                    foreach (var pattern in datePatterns)
                    {
                        var match = Regex.Match(line, pattern);
                        if (match.Success)
                        {
                            var dateStr = match.Value;
                            // Kiểm tra xem có phải là ngày hợp lệ không
                            if (DateTime.TryParse(dateStr, out DateTime parsedDate) &&
                                parsedDate.Year >= 2000 && parsedDate.Year <= DateTime.Now.Year + 1)
                            {
                                company.NgayHoatDong = dateStr;
                                goto DateFound;
                            }
                        }
                    }
                }

                // Nếu vẫn không tìm thấy, thử tìm trong context của "Ngày hoạt động"
                for (int i = 0; i < lines.Length; i++)
                {
                    if (lines[i].Contains("Ngày hoạt động", StringComparison.OrdinalIgnoreCase))
                    {
                        // Tìm trong 3 dòng tiếp theo
                        for (int j = i + 1; j <= Math.Min(i + 3, lines.Length - 1); j++)
                        {
                            var nextLine = lines[j].Trim();
                            foreach (var pattern in datePatterns)
                            {
                                var match = Regex.Match(nextLine, pattern);
                                if (match.Success)
                                {
                                    var dateStr = match.Value;
                                    if (DateTime.TryParse(dateStr, out DateTime parsedDate) &&
                                        parsedDate.Year >= 2000 && parsedDate.Year <= DateTime.Now.Year + 1)
                                    {
                                        company.NgayHoatDong = dateStr;
                                        goto DateFound;
                                    }
                                }
                            }
                        }
                    }
                }

                DateFound:;
            }

            if (string.IsNullOrEmpty(company.LoaiHinhDN))
            {
                // Ưu tiên thông tin từ tên công ty trước (chính xác hơn)
                if (!string.IsNullOrEmpty(company.TenCongTy))
                {
                    if (company.TenCongTy.Contains("HỘ KINH DOANH"))
                        company.LoaiHinhDN = "Hộ kinh doanh cá thể";
                    else if (company.TenCongTy.Contains("TNHH"))
                        company.LoaiHinhDN = "Công ty TNHH";
                    else if (company.TenCongTy.Contains("Cổ phần"))
                        company.LoaiHinhDN = "Công ty cổ phần";
                    else if (company.TenCongTy.Contains("Tư nhân"))
                        company.LoaiHinhDN = "Doanh nghiệp tư nhân";
                }

                // Nếu vẫn không có, tìm trong HTML
                if (string.IsNullOrEmpty(company.LoaiHinhDN))
                {
                    foreach (var line in lines)
                    {
                        if (line.Contains("Hộ kinh doanh cá thể") ||
                            line.Contains("Doanh nghiệp tư nhân") ||
                            line.Contains("Công ty cổ phần") ||
                            (line.Contains("TNHH") && !line.Contains("1 thành viên") && !line.Contains("2 thành viên")))
                        {
                            company.LoaiHinhDN = line;
                            break;
                        }
                    }
                }
            }

            // Fallback cho tình trạng nếu vẫn trống
            if (string.IsNullOrEmpty(company.TinhTrang))
            {
                foreach (var line in lines)
                {
                    if (line.Contains("Đang hoạt động") || line.Contains("Ngừng hoạt động") ||
                        line.Contains("Tạm ngừng") || line.Contains("Giải thể"))
                    {
                        company.TinhTrang = line;
                        break;
                    }
                }
            }

            return company;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Loi khi tra cuu ma so thue {maSoThue}: {ex.Message}");
            return null;
        }
    }

    static void HienThiBang(List<CompanyInfo> companies)
    {
        if (companies.Count == 0) return;

        Console.WriteLine();
        Console.WriteLine("=".PadRight(150, '='));
        Console.WriteLine("KET QUA TRA CUU MA SO THUE");
        Console.WriteLine("=".PadRight(150, '='));

        Console.WriteLine($"{"STT",-4} {"Ma so thue",-15} {"Ten cong ty",-40} {"Nguoi dai dien",-25} {"Tinh trang",-20} {"Ngay hoat dong",-15}");
        Console.WriteLine("-".PadRight(150, '-'));

        for (int i = 0; i < companies.Count; i++)
        {
            var company = companies[i];
            Console.WriteLine($"{i + 1,-4} {TruncateString(company.MaSoThue, 15),-15} {TruncateString(company.TenCongTy, 40),-40} {TruncateString(company.NguoiDaiDien, 25),-25} {TruncateString(company.TinhTrang, 20),-20} {TruncateString(company.NgayHoatDong, 15),-15}");
        }

        Console.WriteLine("-".PadRight(150, '-'));
        Console.WriteLine($"Tong cong: {companies.Count} ket qua");
        Console.WriteLine();

        for (int i = 0; i < companies.Count; i++)
        {
            var company = companies[i];
            Console.WriteLine($"=== CHI TIET CONG TY {i + 1} ===");
            Console.WriteLine($"Ma so thue    : {company.MaSoThue}");
            Console.WriteLine($"Ten cong ty   : {company.TenCongTy}");
            Console.WriteLine($"Dia chi       : {company.DiaChi}");
            Console.WriteLine($"Nguoi dai dien: {company.NguoiDaiDien}");
            Console.WriteLine($"Ngay hoat dong: {company.NgayHoatDong}");
            Console.WriteLine($"Quan ly boi   : {company.QuanLyBoi}");
            Console.WriteLine($"Loai hinh DN  : {company.LoaiHinhDN}");
            Console.WriteLine($"Tinh trang    : {company.TinhTrang}");
            Console.WriteLine();
        }
    }

    static string TruncateString(string input, int maxLength)
    {
        if (string.IsNullOrEmpty(input)) return "";
        if (input.Length <= maxLength) return input;
        return input.Substring(0, maxLength - 3) + "...";
    }

    static async Task<List<string>> DocFileTxt(string filePath)
    {
        var maSoThueList = new List<string>();
        var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);

        foreach (var line in lines)
        {
            var maSoThue = line.Trim();
            if (!string.IsNullOrWhiteSpace(maSoThue))
            {
                maSoThueList.Add(maSoThue);
            }
        }

        return maSoThueList;
    }

    static async Task<List<string>> DocFileExcel(string filePath)
    {
        var maSoThueList = new List<string>();

        // Set license trước khi tạo ExcelPackage
        try
        {
            var licenseProperty = typeof(ExcelPackage).GetProperty("License");
            if (licenseProperty != null && licenseProperty.CanWrite)
            {
                licenseProperty.SetValue(null, "NonCommercial");
            }
        }
        catch { }

        using var package = new ExcelPackage(new FileInfo(filePath));
        var worksheet = package.Workbook.Worksheets[0]; // Lấy sheet đầu tiên

        if (worksheet != null)
        {
            var rowCount = worksheet.Dimension?.Rows ?? 0;

            for (int row = 1; row <= rowCount; row++)
            {
                var cellValue = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                if (!string.IsNullOrWhiteSpace(cellValue))
                {
                    maSoThueList.Add(cellValue);
                }
            }
        }

        return maSoThueList;
    }

    static async Task XuatKetQua(List<CompanyInfo> companies)
    {
        if (companies.Count == 0) return;

        Console.WriteLine("Ban co muon xuat ket qua ra file khong? (y/n): ");
        var choice = Console.ReadLine()?.ToLower();

        if (choice == "y" || choice == "yes")
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            await XuatCSV(companies, $"KetQua_MST_{timestamp}.csv");
            await XuatJSON(companies, $"KetQua_MST_{timestamp}.json");

            Console.WriteLine($"Da xuat file thanh cong:");
            Console.WriteLine($"- CSV: KetQua_MST_{timestamp}.csv");
            Console.WriteLine($"- JSON: KetQua_MST_{timestamp}.json");
        }
    }

    static async Task XuatKetQuaNhieu(List<CompanyInfo> companies)
    {
        if (companies.Count == 0) return;

        Console.WriteLine("Chon dinh dang xuat file:");
        Console.WriteLine("1. Excel (.xlsx)");
        Console.WriteLine("2. JSON");
        Console.WriteLine("3. CSV");
        Console.WriteLine("4. Tat ca cac dinh dang");
        Console.Write("Nhap lua chon: ");

        var choice = Console.ReadLine();
        var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
        var exportedFiles = new List<string>();

        switch (choice)
        {
            case "1":
                await XuatExcel(companies, $"KetQua_MST_{timestamp}.xlsx");
                exportedFiles.Add($"KetQua_MST_{timestamp}.xlsx");
                break;
            case "2":
                await XuatJSON(companies, $"KetQua_MST_{timestamp}.json");
                exportedFiles.Add($"KetQua_MST_{timestamp}.json");
                break;
            case "3":
                await XuatCSV(companies, $"KetQua_MST_{timestamp}.csv");
                exportedFiles.Add($"KetQua_MST_{timestamp}.csv");
                break;
            case "4":
                await XuatExcel(companies, $"KetQua_MST_{timestamp}.xlsx");
                await XuatJSON(companies, $"KetQua_MST_{timestamp}.json");
                await XuatCSV(companies, $"KetQua_MST_{timestamp}.csv");
                exportedFiles.AddRange(new[] {
                    $"KetQua_MST_{timestamp}.xlsx",
                    $"KetQua_MST_{timestamp}.json",
                    $"KetQua_MST_{timestamp}.csv"
                });
                break;
            default:
                Console.WriteLine("Lua chon khong hop le!");
                return;
        }

        Console.WriteLine($"Da xuat file thanh cong:");
        foreach (var file in exportedFiles)
        {
            Console.WriteLine($"- {file}");
        }
    }

    static async Task XuatCSV(List<CompanyInfo> companies, string fileName)
    {
        var csv = new StringBuilder();
        csv.AppendLine("Ma so thue,Ten cong ty,Dia chi,Nguoi dai dien,Ngay hoat dong,Quan ly boi,Loai hinh DN,Tinh trang");

        foreach (var company in companies)
        {
            csv.AppendLine($"\"{company.MaSoThue}\",\"{company.TenCongTy}\",\"{company.DiaChi}\",\"{company.NguoiDaiDien}\",\"{company.NgayHoatDong}\",\"{company.QuanLyBoi}\",\"{company.LoaiHinhDN}\",\"{company.TinhTrang}\"");
        }

        var utf8WithBom = new UTF8Encoding(true);
        await File.WriteAllTextAsync(fileName, csv.ToString(), utf8WithBom);
    }

    static async Task XuatExcel(List<CompanyInfo> companies, string fileName)
    {
        // Set license trước khi tạo ExcelPackage
        try
        {
            var licenseProperty = typeof(ExcelPackage).GetProperty("License");
            if (licenseProperty != null && licenseProperty.CanWrite)
            {
                licenseProperty.SetValue(null, "NonCommercial");
            }
        }
        catch { }

        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Ket Qua Tra Cuu MST");

        // Thiết lập header
        var headers = new[]
        {
            "STT", "Mã số thuế", "Tên công ty", "Địa chỉ", "Người đại diện",
            "Ngày hoạt động", "Quản lý bởi", "Loại hình DN", "Tình trạng"
        };

        // Ghi header
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
            worksheet.Cells[1, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
        }

        // Ghi dữ liệu
        for (int i = 0; i < companies.Count; i++)
        {
            var company = companies[i];
            var row = i + 2;

            worksheet.Cells[row, 1].Value = i + 1;
            worksheet.Cells[row, 2].Value = company.MaSoThue;
            worksheet.Cells[row, 3].Value = company.TenCongTy;
            worksheet.Cells[row, 4].Value = company.DiaChi;
            worksheet.Cells[row, 5].Value = company.NguoiDaiDien;
            worksheet.Cells[row, 6].Value = company.NgayHoatDong;
            worksheet.Cells[row, 7].Value = company.QuanLyBoi;
            worksheet.Cells[row, 8].Value = company.LoaiHinhDN;
            worksheet.Cells[row, 9].Value = company.TinhTrang;

            // Thiết lập border cho từng cell
            for (int col = 1; col <= headers.Length; col++)
            {
                worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }
        }

        // Auto-fit columns
        worksheet.Cells.AutoFitColumns();

        // Thiết lập độ rộng tối đa cho các cột
        for (int col = 1; col <= headers.Length; col++)
        {
            if (worksheet.Column(col).Width > 50)
                worksheet.Column(col).Width = 50;
        }

        // Thiết lập text wrap cho cột địa chỉ và tình trạng
        worksheet.Column(4).Style.WrapText = true; // Địa chỉ
        worksheet.Column(9).Style.WrapText = true; // Tình trạng

        // Lưu file
        var fileInfo = new FileInfo(fileName);
        await package.SaveAsAsync(fileInfo);
    }

    static async Task XuatJSON(List<CompanyInfo> companies, string fileName)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
        };

        var json = JsonSerializer.Serialize(companies, options);
        await File.WriteAllTextAsync(fileName, json, Encoding.UTF8);
    }
}
