using System;
using System.Drawing;
using System.Windows.Forms;

namespace KiemtraMST
{
    public partial class TestDataGridForm : Form
    {
        private DataGridView dataGridView;
        private Button testButton;
        private Label statusLabel;

        public TestDataGridForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "Test DataGridView";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Create DataGridView
            dataGridView = new DataGridView();
            dataGridView.Location = new Point(10, 10);
            dataGridView.Size = new Size(760, 400);
            dataGridView.BackgroundColor = Color.LightYellow;
            dataGridView.BorderStyle = BorderStyle.Fixed3D;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = true;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.RowHeadersVisible = false;
            dataGridView.AutoGenerateColumns = false;

            // Create test button
            testButton = new Button();
            testButton.Text = "Add Test Data";
            testButton.Location = new Point(10, 420);
            testButton.Size = new Size(150, 30);
            testButton.Click += TestButton_Click;

            // Create status label
            statusLabel = new Label();
            statusLabel.Text = "Ready";
            statusLabel.Location = new Point(10, 460);
            statusLabel.Size = new Size(760, 20);

            // Add controls to form
            this.Controls.Add(dataGridView);
            this.Controls.Add(testButton);
            this.Controls.Add(statusLabel);
        }

        private void TestButton_Click(object sender, EventArgs e)
        {
            try
            {
                statusLabel.Text = "Adding test data...";
                
                // Clear existing data
                dataGridView.Columns.Clear();
                dataGridView.Rows.Clear();

                // Add columns
                dataGridView.Columns.Add("Col1", "Column 1");
                dataGridView.Columns.Add("Col2", "Column 2");
                dataGridView.Columns.Add("Col3", "Column 3");

                // Set column widths
                dataGridView.Columns[0].Width = 200;
                dataGridView.Columns[1].Width = 200;
                dataGridView.Columns[2].Width = 200;

                // Add test rows
                for (int i = 1; i <= 5; i++)
                {
                    int rowIndex = dataGridView.Rows.Add();
                    dataGridView.Rows[rowIndex].Cells[0].Value = $"Row {i} Col 1";
                    dataGridView.Rows[rowIndex].Cells[1].Value = $"Row {i} Col 2";
                    dataGridView.Rows[rowIndex].Cells[2].Value = $"Row {i} Col 3";
                }

                // Force refresh
                dataGridView.Refresh();
                dataGridView.Update();
                dataGridView.Invalidate();
                Application.DoEvents();

                statusLabel.Text = $"Added {dataGridView.Rows.Count} rows, {dataGridView.Columns.Count} columns";
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"Error: {ex.Message}";
                MessageBox.Show($"Error: {ex.Message}\n\nStack: {ex.StackTrace}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
