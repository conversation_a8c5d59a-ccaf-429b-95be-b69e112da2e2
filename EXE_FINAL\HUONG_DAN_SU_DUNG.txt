🏢 ỨNG DỤNG TRA CỨU MÃ SỐ THUẾ DOANH NGHIỆP
================================================

📋 THÔNG TIN CHUNG
- Phiên bản: 1.0.0
- Ngày tạo: 2025-06-23
- T<PERSON><PERSON> giả: Augment Agent
- Hỗ trợ: Windows 10/11

🚀 CÁCH SỬ DỤNG

1️⃣ TRA CỨU ĐỢN LẺ:
   - Chọn radio "Tra cứu mã số thuế đơn lẻ"
   - <PERSON>hập mã số thuế (VD: 0109153660)
   - <PERSON><PERSON>ấn "Tra cứu"

2️⃣ TRA CỨU HÀNG LOẠT:
   - Chọn radio "Tra cứu nhiều mã số thuế từ file"
   - Nhấn "Chọn file" và chọn file .txt/.xls/.xlsx
   - Nhấn "Tra cứu"

3️⃣ XUẤT KẾT QUẢ:
   - Chọn dòng cần xuất (hoặc không chọn để xuất tất cả)
   - <PERSON>hấn "Xuất đã chọn" hoặc "Xuất tất cả"
   - Chọn định dạng: Excel, JSON, hoặc CSV

📁 ĐỊNH DẠNG FILE ĐẦU VÀO
- .txt: Mỗi dòng một mã số thuế
- .xls/.xlsx: Mã số thuế ở cột A, mỗi dòng một mã

📊 THÔNG TIN TRA CỨU ĐƯỢC
- Mã số thuế
- Tên công ty
- Địa chỉ
- Người đại diện
- Ngày hoạt động
- Quản lý bởi
- Loại hình doanh nghiệp
- Tình trạng

🧪 MÃ SỐ THUẾ TEST
- 0109153660 (CÔNG TY TNHH CÔNG NGHỆ VÀ GIẢI PHÁP PHẦN MỀM DTECH)
- 0100109106 (CÔNG TY CỔ PHẦN THƯƠNG MẠI DỊCH VỤ PHONG VŨ)

📝 LƯU Ý
- Cần kết nối internet để tra cứu
- Mã số thuế phải có 10 hoặc 13 ký tự
- Ứng dụng tự động lưu lịch sử tra cứu
- File xuất sử dụng encoding UTF-8 cho tiếng Việt

🔧 XỬ LÝ LỖI
- Nếu không tra cứu được: Kiểm tra internet và thử lại
- Nếu file không đọc được: Kiểm tra định dạng file
- Nếu mã số thuế không hợp lệ: Kiểm tra định dạng 10-13 ký tự

📞 HỖ TRỢ
Nếu gặp vấn đề, vui lòng:
1. Kiểm tra kết nối internet
2. Thử với mã số thuế khác
3. Restart ứng dụng

© 2025 - Ứng dụng tra cứu mã số thuế doanh nghiệp
