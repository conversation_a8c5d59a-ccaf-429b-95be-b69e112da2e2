using HtmlAgilityPack;
using System.Net.Http;
using System.Threading.Tasks;
using System;

public class Program
{
    public static async Task Main(string[] args)
    {
        string url = "https://masothue.com/Search/?q=0700617704-001";

        // 1. Dùng HttpClient để tải HTML
        HttpClient client = new HttpClient();
        // Một số website yêu cầu User-Agent để trả về kết quả
        client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Wdotnetindows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        
        string htmlContent = await client.GetStringAsync(url);

        // 2. Phân tích cú pháp HTML bằng HtmlAgilityPack
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(htmlContent);

        // 3. T<PERSON><PERSON> vấn phần tử bằng XPath
        // XPath: "//td[@itemprop='address']" có nghĩa là:
        // "//"     : Tìm ở bất cứ đâu trong tài liệu
        // "td"      : Thẻ phải là <td>
        // "[@...]"  : Có thuộc tính...
        // "@itemprop='address'" : ...là itemprop với giá trị 'address'
        var addressNode = htmlDoc.DocumentNode.SelectSingleNode("//td[@itemprop='address']");

        // 4. Trích xuất và hiển thị dữ liệu
        if (addressNode != null)
        {
            // .InnerText sẽ lấy toàn bộ văn bản bên trong nút này
            string address = addressNode.InnerText.Trim(); // .Trim() để xóa khoảng trắng thừa
            Console.WriteLine("--- Dùng HtmlAgilityPack ---");
            Console.WriteLine($"Địa chỉ tìm được: {address}");
        }
        else
        {
            Console.WriteLine("Không tìm thấy thẻ địa chỉ.");
        }
    }
}