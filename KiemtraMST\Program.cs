using HtmlAgilityPack;
using System.Net.Http;
using System.Threading.Tasks;
using System;

public class Program
{
    public static async Task Main(string[] args)
    {
        string url = "https://masothue.com/Search/?q=0700617704-001";

        // 1. Dùng HttpClient để tải HTML
        HttpClient client = new HttpClient();
        // Một số website yêu cầu User-Agent để trả về kết quả
        client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Wdotnetindows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        
        string htmlContent = await client.GetStringAsync(url);

        // 2. Phân tích cú pháp HTML bằng HtmlAgilityPack
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(htmlContent);

        // 3. T<PERSON>y vấn các phần tử từ kết quả tìm kiếm
        Console.WriteLine("--- <PERSON>ùng HtmlAgilityPack ---");
        Console.WriteLine("Đang tìm kiếm thông tin công ty...");

        // Tìm tất cả các kết quả công ty trong trang
        var companyNodes = htmlDoc.DocumentNode.SelectNodes("//h3/a[contains(@href, '/')]");

        if (companyNodes != null && companyNodes.Count > 0)
        {
            Console.WriteLine($"Tìm thấy {companyNodes.Count} kết quả:");
            Console.WriteLine();

            for (int i = 0; i < Math.Min(companyNodes.Count, 3); i++) // Chỉ hiển thị 3 kết quả đầu tiên
            {
                var companyNode = companyNodes[i];

                // Lấy tên công ty
                string companyName = companyNode.InnerText.Trim();

                // Tìm thông tin chi tiết của công ty này
                var parentDiv = companyNode.Ancestors().FirstOrDefault(x => x.Name == "div" || x.Name == "section");
                if (parentDiv == null)
                {
                    // Thử tìm parent khác
                    parentDiv = companyNode.ParentNode?.ParentNode?.ParentNode;
                }

                if (parentDiv != null)
                {
                    // Tìm mã số thuế
                    var taxCodeNode = parentDiv.SelectSingleNode(".//text()[contains(., 'Mã số thuế:')]");
                    string taxCode = "";
                    if (taxCodeNode != null)
                    {
                        var taxCodeLink = taxCodeNode.ParentNode?.SelectSingleNode(".//a");
                        if (taxCodeLink != null)
                        {
                            taxCode = taxCodeLink.InnerText.Trim();
                        }
                    }

                    // Tìm người đại diện
                    var representativeNode = parentDiv.SelectSingleNode(".//text()[contains(., 'Người đại diện:')]");
                    string representative = "";
                    if (representativeNode != null)
                    {
                        var repLink = representativeNode.ParentNode?.SelectSingleNode(".//a");
                        if (repLink != null)
                        {
                            representative = repLink.InnerText.Trim();
                        }
                    }

                    // Tìm địa chỉ (thường là text cuối cùng trong div)
                    var allTextNodes = parentDiv.SelectNodes(".//text()");
                    string address = "";
                    if (allTextNodes != null)
                    {
                        // Tìm text node có chứa địa chỉ (có "Phường", "Quận", "Thành phố", "Tỉnh", "Huyện")
                        foreach (var textNode in allTextNodes.Reverse())
                        {
                            var text = textNode.InnerText.Trim();
                            if (text.Length > 20 && (text.Contains("Phường") || text.Contains("Quận") ||
                                text.Contains("Thành phố") || text.Contains("Tỉnh") || text.Contains("Huyện") ||
                                text.Contains("Xã") || text.Contains("TP")))
                            {
                                address = text;
                                break;
                            }
                        }
                    }

                    // Hiển thị thông tin
                    Console.WriteLine($"=== Kết quả {i + 1} ===");
                    Console.WriteLine($"Tên công ty: {companyName}");
                    Console.WriteLine($"Mã số thuế: {taxCode}");
                    Console.WriteLine($"Người đại diện: {representative}");
                    Console.WriteLine($"Địa chỉ: {address}");
                    Console.WriteLine();
                }
            }
        }
        else
        {
            Console.WriteLine("Không tìm thấy kết quả nào cho mã số thuế này.");
            Console.WriteLine("Có thể mã số thuế không tồn tại hoặc chưa được cập nhật trong cơ sở dữ liệu.");
        }
    }
}