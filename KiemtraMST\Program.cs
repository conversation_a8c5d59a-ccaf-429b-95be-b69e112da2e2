using HtmlAgilityPack;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using System.Linq;
using System.Web;
using System.Text;
using System.Text.Json;
using System.IO;
using System.Collections.Generic;
using System.Text.Encodings.Web;
using System.Text.Unicode;

// Class để lưu thông tin công ty
public class CompanyInfo
{
    public string TenCongTy { get; set; } = "";
    public string MaSoThue { get; set; } = "";
    public string DiaChi { get; set; } = "";
    public string NguoiDaiDien { get; set; } = "";
    public string NgayHoatDong { get; set; } = "";
    public string QuanLyBoi { get; set; } = "";
    public string LoaiHinhDN { get; set; } = "";
    public string TinhTrang { get; set; } = "";
}

public class Program
{
    public static async Task Main(string[] args)
    {
        try
        {
            // Thiết lập encoding UTF-8 cho console
            Console.OutputEncoding = Encoding.UTF8;
            Console.InputEncoding = Encoding.UTF8;
        }
        catch
        {
            // Nếu không set được encoding, tiếp tục chạy
        }

        Console.WriteLine("=== CHUONG TRINH TRA CUU MA SO THUE ===");
        Console.WriteLine();

        // Test đơn giản trước
        Console.WriteLine("Chon chuc nang:");
        Console.WriteLine("1. Tra cuu theo ma so thue");
        Console.WriteLine("2. Tra cuu theo ten cong ty");
        Console.WriteLine("3. Tra cuu nhieu ma so thue tu file");
        Console.WriteLine("0. Thoat");
        Console.Write("Nhap lua chon: ");

        var choice = Console.ReadLine();

        switch (choice)
        {
            case "1":
                await TraCuuTheoMaSoThue();
                break;
            case "2":
                Console.WriteLine("Chuc nang dang phat trien...");
                break;
            case "3":
                Console.WriteLine("Chuc nang dang phat trien...");
                break;
            case "0":
                return;
            default:
                Console.WriteLine("Lua chon khong hop le!");
                break;
        }
    }


    // Method tra cứu theo mã số thuế
    static async Task TraCuuTheoMaSoThue()
    {
        Console.Write("Nhập mã số thuế: ");
        var maSoThue = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(maSoThue))
        {
            Console.WriteLine("Mã số thuế không được để trống!");
            return;
        }

        var companies = new List<CompanyInfo>();
        var company = await LayThongTinCongTy(maSoThue);

        if (company != null)
        {
            companies.Add(company);
            HienThiBang(companies);
            await XuatKetQua(companies);
        }
        else
        {
            Console.WriteLine("Không tìm thấy thông tin cho mã số thuế này!");
        }
    }

    // Method tra cứu theo tên công ty
    static async Task TraCuuTheoTenCongTy()
    {
        Console.Write("Nhập tên công ty: ");
        var tenCongTy = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(tenCongTy))
        {
            Console.WriteLine("Tên công ty không được để trống!");
            return;
        }

        var companies = await TimKiemTheoTen(tenCongTy);

        if (companies.Count > 0)
        {
            HienThiBang(companies);
            await XuatKetQua(companies);
        }
        else
        {
            Console.WriteLine("Không tìm thấy công ty nào với tên này!");
        }
    }

    // Method tra cứu nhiều mã số thuế
    static async Task TraCuuNhieuMaSoThue()
    {
        Console.WriteLine("Nhập danh sách mã số thuế (mỗi mã một dòng), nhập 'END' để kết thúc:");

        var danhSachMST = new List<string>();
        string input;

        while ((input = Console.ReadLine()) != "END")
        {
            if (!string.IsNullOrWhiteSpace(input))
            {
                danhSachMST.Add(input.Trim());
            }
        }

        if (danhSachMST.Count == 0)
        {
            Console.WriteLine("Không có mã số thuế nào để tra cứu!");
            return;
        }

        var companies = new List<CompanyInfo>();

        Console.WriteLine($"Đang tra cứu {danhSachMST.Count} mã số thuế...");

        for (int i = 0; i < danhSachMST.Count; i++)
        {
            Console.WriteLine($"Đang xử lý {i + 1}/{danhSachMST.Count}: {danhSachMST[i]}");

            var company = await LayThongTinCongTy(danhSachMST[i]);
            if (company != null)
            {
                companies.Add(company);
            }

            // Delay để tránh spam server
            await Task.Delay(1000);
        }

        if (companies.Count > 0)
        {
            HienThiBang(companies);
            await XuatKetQua(companies);
        }
        else
        {
            Console.WriteLine("Không tìm thấy thông tin cho bất kỳ mã số thuế nào!");
        }
    }

    // Method lấy thông tin công ty từ mã số thuế
    static async Task<CompanyInfo?> LayThongTinCongTy(string maSoThue)
    {
        try
        {
            string url = $"https://masothue.com/Search/?q={maSoThue}&type=auto&token=&force-search=0";

            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            client.DefaultRequestHeaders.Add("Accept-Language", "vi-VN,vi;q=0.9,en;q=0.8");
            client.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
            client.DefaultRequestHeaders.Add("DNT", "1");
            client.DefaultRequestHeaders.Add("Connection", "keep-alive");
            client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");

            string htmlContent = await client.GetStringAsync(url);
            var htmlDoc = new HtmlDocument();
            htmlDoc.LoadHtml(htmlContent);

            var company = new CompanyInfo();

            // Lấy tên công ty
            var companyNameNode = htmlDoc.DocumentNode.SelectSingleNode("//h1") ??
                                 htmlDoc.DocumentNode.SelectSingleNode("//title");
            if (companyNameNode != null)
            {
                var fullTitle = companyNameNode.InnerText.Trim();
                if (fullTitle.Contains(" - "))
                {
                    var parts = fullTitle.Split(" - ");
                    if (parts.Length > 1)
                    {
                        company.TenCongTy = parts[1].Trim();
                        company.MaSoThue = parts[0].Trim();
                    }
                }
                else
                {
                    company.TenCongTy = fullTitle;
                }
            }

            // Nếu chưa có mã số thuế, lấy từ URL
            if (string.IsNullOrEmpty(company.MaSoThue))
            {
                company.MaSoThue = maSoThue;
            }

            // Lấy các thông tin khác
            company.DiaChi = LayThongTinTheoLabel(htmlDoc, "Địa chỉ");
            company.NgayHoatDong = LayThongTinTheoLabel(htmlDoc, "Ngày hoạt động");
            company.QuanLyBoi = LayThongTinTheoLabel(htmlDoc, "Quản lý bởi");
            company.LoaiHinhDN = LayThongTinTheoLabel(htmlDoc, "Loại hình DN");
            company.TinhTrang = LayThongTinTheoLabel(htmlDoc, "Tình trạng");

            // Lấy người đại diện
            var representativeNode = htmlDoc.DocumentNode.SelectSingleNode("//text()[contains(., 'Người đại diện')]");
            if (representativeNode != null)
            {
                var repLink = representativeNode.ParentNode?.ParentNode?.SelectSingleNode(".//a[contains(@href, 'legalName')]");
                if (repLink != null)
                {
                    company.NguoiDaiDien = repLink.InnerText.Trim();
                }
            }

            return company;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Lỗi khi tra cứu mã số thuế {maSoThue}: {ex.Message}");
            return null;
        }
    }

    // Method helper để lấy thông tin theo label
    static string LayThongTinTheoLabel(HtmlDocument htmlDoc, string label)
    {
        var labelNode = htmlDoc.DocumentNode.SelectSingleNode($"//text()[contains(., '{label}')]");
        if (labelNode != null)
        {
            var parent = labelNode.ParentNode;
            if (parent != null)
            {
                var nextSibling = parent.NextSibling;
                while (nextSibling != null)
                {
                    var text = nextSibling.InnerText.Trim();
                    if (!string.IsNullOrEmpty(text) && text.Length > 2)
                    {
                        return text;
                    }
                    nextSibling = nextSibling.NextSibling;
                }
            }
        }
        return "";
    }

    // Method tìm kiếm theo tên công ty
    static async Task<List<CompanyInfo>> TimKiemTheoTen(string tenCongTy)
    {
        var companies = new List<CompanyInfo>();

        try
        {
            string url = $"https://masothue.com/Search/?q={Uri.EscapeDataString(tenCongTy)}&type=auto&token=&force-search=0";

            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            client.DefaultRequestHeaders.Add("Accept-Language", "vi-VN,vi;q=0.9,en;q=0.8");
            client.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
            client.DefaultRequestHeaders.Add("DNT", "1");
            client.DefaultRequestHeaders.Add("Connection", "keep-alive");
            client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");

            string htmlContent = await client.GetStringAsync(url);
            var htmlDoc = new HtmlDocument();
            htmlDoc.LoadHtml(htmlContent);

            // Tìm các kết quả tìm kiếm
            var companyLinks = htmlDoc.DocumentNode.SelectNodes("//h3/a[contains(@href, '/')]");

            if (companyLinks != null)
            {
                foreach (var link in companyLinks.Take(5)) // Lấy tối đa 5 kết quả
                {
                    var href = link.GetAttributeValue("href", "");
                    if (!string.IsNullOrEmpty(href))
                    {
                        // Trích xuất mã số thuế từ URL
                        var maSoThue = TrichXuatMaSoThueFromUrl(href);
                        if (!string.IsNullOrEmpty(maSoThue))
                        {
                            var company = await LayThongTinCongTy(maSoThue);
                            if (company != null)
                            {
                                companies.Add(company);
                            }

                            // Delay để tránh spam
                            await Task.Delay(500);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Lỗi khi tìm kiếm theo tên: {ex.Message}");
        }

        return companies;
    }

    // Method trích xuất mã số thuế từ URL
    static string TrichXuatMaSoThueFromUrl(string url)
    {
        // URL dạng: /0700617704-001-ho-kinh-doanh-tap-hoa-hung-hung
        var parts = url.Split('-');
        if (parts.Length >= 2)
        {
            var maSoThue = parts[0].TrimStart('/') + "-" + parts[1];
            return maSoThue;
        }
        return "";
    }

    // Method hiển thị bảng kết quả
    static void HienThiBang(List<CompanyInfo> companies)
    {
        if (companies.Count == 0)
        {
            Console.WriteLine("Không có dữ liệu để hiển thị.");
            return;
        }

        Console.WriteLine();
        Console.WriteLine("=".PadRight(150, '='));
        Console.WriteLine("KẾT QUẢ TRA CỨU MÃ SỐ THUẾ");
        Console.WriteLine("=".PadRight(150, '='));

        // Header
        Console.WriteLine($"{"STT",-4} {"Mã số thuế",-15} {"Tên công ty",-40} {"Người đại diện",-25} {"Tình trạng",-20} {"Ngày hoạt động",-15}");
        Console.WriteLine("-".PadRight(150, '-'));

        // Data rows
        for (int i = 0; i < companies.Count; i++)
        {
            var company = companies[i];
            Console.WriteLine($"{(i + 1),-4} {TruncateString(company.MaSoThue, 15),-15} {TruncateString(company.TenCongTy, 40),-40} {TruncateString(company.NguoiDaiDien, 25),-25} {TruncateString(company.TinhTrang, 20),-20} {TruncateString(company.NgayHoatDong, 15),-15}");
        }

        Console.WriteLine("-".PadRight(150, '-'));
        Console.WriteLine($"Tổng cộng: {companies.Count} kết quả");
        Console.WriteLine();

        // Hiển thị chi tiết từng công ty
        for (int i = 0; i < companies.Count; i++)
        {
            var company = companies[i];
            Console.WriteLine($"=== CHI TIẾT CÔNG TY {i + 1} ===");
            Console.WriteLine($"Mã số thuế    : {company.MaSoThue}");
            Console.WriteLine($"Tên công ty   : {company.TenCongTy}");
            Console.WriteLine($"Địa chỉ       : {company.DiaChi}");
            Console.WriteLine($"Người đại diện: {company.NguoiDaiDien}");
            Console.WriteLine($"Ngày hoạt động: {company.NgayHoatDong}");
            Console.WriteLine($"Quản lý bởi   : {company.QuanLyBoi}");
            Console.WriteLine($"Loại hình DN  : {company.LoaiHinhDN}");
            Console.WriteLine($"Tình trạng    : {company.TinhTrang}");
            Console.WriteLine();
        }
    }

    // Method cắt chuỗi nếu quá dài
    static string TruncateString(string input, int maxLength)
    {
        if (string.IsNullOrEmpty(input))
            return "";

        if (input.Length <= maxLength)
            return input;

        return input.Substring(0, maxLength - 3) + "...";
    }

    // Method xuất kết quả ra file
    static async Task XuatKetQua(List<CompanyInfo> companies)
    {
        if (companies.Count == 0)
            return;

        Console.WriteLine("Bạn có muốn xuất kết quả ra file không? (y/n): ");
        var choice = Console.ReadLine()?.ToLower();

        if (choice == "y" || choice == "yes")
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            // Xuất CSV cho Excel
            await XuatCSV(companies, $"KetQua_MST_{timestamp}.csv");

            // Xuất JSON
            await XuatJSON(companies, $"KetQua_MST_{timestamp}.json");

            Console.WriteLine($"Đã xuất file thành công:");
            Console.WriteLine($"- CSV: KetQua_MST_{timestamp}.csv");
            Console.WriteLine($"- JSON: KetQua_MST_{timestamp}.json");
        }
    }

    // Method xuất CSV
    static async Task XuatCSV(List<CompanyInfo> companies, string fileName)
    {
        var csv = new StringBuilder();

        // Header với BOM để Excel hiển thị đúng tiếng Việt
        csv.AppendLine("Mã số thuế,Tên công ty,Địa chỉ,Người đại diện,Ngày hoạt động,Quản lý bởi,Loại hình DN,Tình trạng");

        // Data
        foreach (var company in companies)
        {
            csv.AppendLine($"\"{company.MaSoThue}\",\"{company.TenCongTy}\",\"{company.DiaChi}\",\"{company.NguoiDaiDien}\",\"{company.NgayHoatDong}\",\"{company.QuanLyBoi}\",\"{company.LoaiHinhDN}\",\"{company.TinhTrang}\"");
        }

        // Ghi file với BOM UTF-8
        var utf8WithBom = new UTF8Encoding(true);
        await File.WriteAllTextAsync(fileName, csv.ToString(), utf8WithBom);
    }

    // Method xuất JSON
    static async Task XuatJSON(List<CompanyInfo> companies, string fileName)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
        };

        var json = JsonSerializer.Serialize(companies, options);
        await File.WriteAllTextAsync(fileName, json, Encoding.UTF8);
    }
}