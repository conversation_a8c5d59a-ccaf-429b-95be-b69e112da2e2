using HtmlAgilityPack;
using System.Net.Http;
using System.Threading.Tasks;
using System;

public class Program
{
    public static async Task Main(string[] args)
    {
        string url = "https://masothue.com/Search/?q=0700617704-001";

        // 1. Dùng HttpClient để tải HTML
        HttpClient client = new HttpClient();
        // Một số website yêu cầu User-Agent để trả về kết quả
        client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Wdotnetindows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        
        string htmlContent = await client.GetStringAsync(url);

        // 2. Phân tích cú pháp HTML bằng HtmlAgilityPack
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(htmlContent);

        // 3. T<PERSON>y vấn phần tử bằng XPath
        // Thử nhiều XPath khác nhau để tìm địa chỉ
        var addressNode = htmlDoc.DocumentNode.SelectSingleNode("//td[@itemprop='address']");

        // Nếu không tìm thấy với XPath cũ, thử các XPath khác
        if (addressNode == null)
        {
            // Thử tìm trong bảng thông tin công ty
            addressNode = htmlDoc.DocumentNode.SelectSingleNode("//tr[td[contains(text(), 'Địa chỉ')]]/td[2]");
        }

        if (addressNode == null)
        {
            // Thử tìm theo pattern khác
            addressNode = htmlDoc.DocumentNode.SelectSingleNode("//td[contains(@class, 'address')] | //div[contains(@class, 'address')]");
        }

        if (addressNode == null)
        {
            // Thử tìm text chứa địa chỉ điển hình (có "Phường", "Quận", "Thành phố")
            var allNodes = htmlDoc.DocumentNode.SelectNodes("//text()[contains(., 'Phường') and contains(., 'Quận')]");
            if (allNodes != null && allNodes.Count > 0)
            {
                addressNode = allNodes[0].ParentNode;
            }
        }

        // 4. Trích xuất và hiển thị dữ liệu
        if (addressNode != null)
        {
            // .InnerText sẽ lấy toàn bộ văn bản bên trong nút này
            string address = addressNode.InnerText.Trim(); // .Trim() để xóa khoảng trắng thừa
            Console.WriteLine("--- Dùng HtmlAgilityPack ---");
            Console.WriteLine($"Địa chỉ tìm được: {address}");
        }
        else
        {
            Console.WriteLine("Không tìm thấy thẻ địa chỉ.");
            Console.WriteLine("Đang thử tìm kiếm trong toàn bộ nội dung...");

            // Debug: In ra một phần HTML để kiểm tra
            var bodyContent = htmlDoc.DocumentNode.SelectSingleNode("//body")?.InnerText;
            if (bodyContent != null && bodyContent.Length > 500)
            {
                Console.WriteLine("Nội dung trang web (500 ký tự đầu):");
                Console.WriteLine(bodyContent.Substring(0, 500) + "...");
            }
        }
    }
}