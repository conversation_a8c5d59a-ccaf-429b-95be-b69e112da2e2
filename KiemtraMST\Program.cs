using HtmlAgilityPack;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using System.Linq;

public class Program
{
    public static async Task Main(string[] args)
    {
        string url = "https://masothue.com/Search/?q=0700617704-001&type=auto&token=&force-search=0";

        // 1. Dùng HttpClient để tải HTML
        HttpClient client = new HttpClient();
        // Một số website yêu cầu User-Agent để trả về kết quả
        client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Wdotnetindows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        
        string htmlContent = await client.GetStringAsync(url);

        // 2. Phân tích cú pháp HTML bằng HtmlAgilityPack
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(htmlContent);

        // 3. Truy vấn thông tin công ty từ trang chi tiết
        Console.WriteLine("--- Dùng HtmlAgilityPack ---");
        Console.WriteLine("Đang trích xuất thông tin công ty...");

        // Tìm tên công ty từ tiêu đề trang hoặc heading chính
        var companyNameNode = htmlDoc.DocumentNode.SelectSingleNode("//h1") ??
                             htmlDoc.DocumentNode.SelectSingleNode("//title");
        string companyName = "";
        if (companyNameNode != null)
        {
            companyName = companyNameNode.InnerText.Trim();
            // Loại bỏ phần mã số thuế khỏi tên nếu có
            if (companyName.Contains(" - "))
            {
                var parts = companyName.Split(" - ");
                if (parts.Length > 1)
                {
                    companyName = parts[1].Trim();
                }
            }
        }

        // Tìm mã số thuế - thử nhiều cách khác nhau
        string taxCode = "";

        // Cách 1: Tìm từ URL hoặc tiêu đề trang
        if (companyNameNode != null)
        {
            var fullTitle = companyNameNode.InnerText.Trim();
            if (fullTitle.Contains(" - "))
            {
                var parts = fullTitle.Split(" - ");
                if (parts.Length > 0)
                {
                    var potentialTaxCode = parts[0].Trim();
                    if (potentialTaxCode.Length > 5 && (potentialTaxCode.Contains("-") || char.IsDigit(potentialTaxCode[0])))
                    {
                        taxCode = potentialTaxCode;
                    }
                }
            }
        }

        // Cách 2: Tìm trong bảng thông tin
        if (string.IsNullOrEmpty(taxCode))
        {
            var taxCodeNode = htmlDoc.DocumentNode.SelectSingleNode("//text()[contains(., 'Mã số thuế')]");
            if (taxCodeNode != null)
            {
                var parent = taxCodeNode.ParentNode;
                if (parent != null)
                {
                    var nextSibling = parent.NextSibling;
                    while (nextSibling != null)
                    {
                        if (nextSibling.NodeType == HtmlAgilityPack.HtmlNodeType.Text)
                        {
                            var text = nextSibling.InnerText.Trim();
                            if (text.Length > 5 && (text.Contains('-') || char.IsDigit(text[0])))
                            {
                                taxCode = text;
                                break;
                            }
                        }
                        else if (nextSibling.NodeType == HtmlAgilityPack.HtmlNodeType.Element)
                        {
                            var text = nextSibling.InnerText.Trim();
                            if (text.Length > 5 && (text.Contains('-') || char.IsDigit(text[0])))
                            {
                                taxCode = text;
                                break;
                            }
                        }
                        nextSibling = nextSibling.NextSibling;
                    }
                }
            }
        }

        // Cách 3: Tìm từ URL nếu vẫn chưa có
        if (string.IsNullOrEmpty(taxCode))
        {
            // Lấy từ URL ban đầu
            var uri = new Uri(url);
            var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
            var qParam = query["q"];
            if (!string.IsNullOrEmpty(qParam))
            {
                taxCode = qParam;
            }
        }

        // Tìm địa chỉ
        string address = "";
        var addressNode = htmlDoc.DocumentNode.SelectSingleNode("//text()[contains(., 'Địa chỉ')]");
        if (addressNode != null)
        {
            var parent = addressNode.ParentNode;
            if (parent != null)
            {
                var nextSibling = parent.NextSibling;
                while (nextSibling != null)
                {
                    var text = nextSibling.InnerText.Trim();
                    if (text.Length > 10 && (text.Contains("Phường") || text.Contains("Quận") ||
                        text.Contains("Thành phố") || text.Contains("Tỉnh") || text.Contains("Huyện") ||
                        text.Contains("Xã") || text.Contains("TP") || text.Contains("Số")))
                    {
                        address = text;
                        break;
                    }
                    nextSibling = nextSibling.NextSibling;
                }
            }
        }

        // Tìm người đại diện
        string representative = "";
        var representativeNode = htmlDoc.DocumentNode.SelectSingleNode("//text()[contains(., 'Người đại diện')]");
        if (representativeNode != null)
        {
            var parent = representativeNode.ParentNode;
            if (parent != null)
            {
                // Tìm link chứa tên người đại diện
                var repLink = parent.ParentNode?.SelectSingleNode(".//a[contains(@href, 'legalName')]");
                if (repLink != null)
                {
                    representative = repLink.InnerText.Trim();
                }
            }
        }

        // Hiển thị kết quả
        Console.WriteLine();
        Console.WriteLine("=== THÔNG TIN CÔNG TY ===");
        Console.WriteLine($"Tên công ty: {companyName}");
        Console.WriteLine($"Mã số thuế: {taxCode}");
        Console.WriteLine($"Người đại diện: {representative}");
        Console.WriteLine($"Địa chỉ: {address}");

        // Kiểm tra xem có tìm thấy thông tin không
        if (string.IsNullOrEmpty(companyName) && string.IsNullOrEmpty(taxCode))
        {
            Console.WriteLine();
            Console.WriteLine("Không tìm thấy thông tin công ty. Có thể:");
            Console.WriteLine("1. Mã số thuế không tồn tại");
            Console.WriteLine("2. Cấu trúc trang web đã thay đổi");
            Console.WriteLine("3. Cần cập nhật XPath selectors");
        }
    }
}