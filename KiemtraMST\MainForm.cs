using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Text.Json;
using System.Text.Encodings.Web;
using System.Text.Unicode;

namespace KiemtraMST
{
    public partial class MainForm : Form
    {
        private List<CompanyInfo> _companies = new List<CompanyInfo>();
        private List<string> _searchHistory = new List<string>();
        private TaxService _taxService = new TaxService();

        public MainForm()
        {
            InitializeComponent();
            LoadSearchHistory();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "Chương trình tra cứu mã số thuế";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1000, 600);
            this.Icon = SystemIcons.Application;

            // <PERSON>reate controls
            CreateMenuStrip();
            CreateToolStrip();
            CreateMainPanel();
            CreateStatusStrip();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private MenuStrip menuStrip;
        private ToolStrip toolStrip;
        private Panel mainPanel;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private ToolStripProgressBar progressBar;

        // Input controls
        private GroupBox inputGroupBox;
        private RadioButton singleTaxCodeRadio;
        private RadioButton multipleTaxCodeRadio;
        private TextBox singleTaxCodeTextBox;
        private ComboBox historyComboBox;
        private TextBox filePathTextBox;
        private Button browseFileButton;
        private Button searchButton;
        private Button clearButton;

        // Results controls
        private GroupBox resultsGroupBox;
        private DataGridView resultsDataGridView;
        private Panel resultsButtonPanel;
        private Button exportSelectedButton;
        private Button exportAllButton;
        private Button clearResultsButton;

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip();
            
            // File menu
            var fileMenu = new ToolStripMenuItem("&File");
            fileMenu.DropDownItems.Add("&Mở file...", null, OpenFile_Click);
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("&Xuất Excel...", null, ExportExcel_Click);
            fileMenu.DropDownItems.Add("&Xuất JSON...", null, ExportJSON_Click);
            fileMenu.DropDownItems.Add("&Xuất CSV...", null, ExportCSV_Click);
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("&Thoát", null, (s, e) => this.Close());

            // Tools menu
            var toolsMenu = new ToolStripMenuItem("&Công cụ");
            toolsMenu.DropDownItems.Add("&Xóa lịch sử", null, ClearHistory_Click);
            toolsMenu.DropDownItems.Add("&Xóa kết quả", null, ClearResults_Click);

            // Help menu
            var helpMenu = new ToolStripMenuItem("&Trợ giúp");
            helpMenu.DropDownItems.Add("&Hướng dẫn", null, ShowHelp_Click);
            helpMenu.DropDownItems.Add("&Về chương trình", null, ShowAbout_Click);

            menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, toolsMenu, helpMenu });
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip();
            toolStrip.ImageScalingSize = new Size(24, 24);

            var openButton = new ToolStripButton("Mở file", null, OpenFile_Click);
            var searchButton = new ToolStripButton("Tra cứu", null, Search_Click);
            var exportButton = new ToolStripButton("Xuất file", null, ExportAll_Click);
            var clearButton = new ToolStripButton("Xóa kết quả", null, ClearResults_Click);

            toolStrip.Items.AddRange(new ToolStripItem[] {
                openButton,
                new ToolStripSeparator(),
                searchButton,
                new ToolStripSeparator(),
                exportButton,
                new ToolStripSeparator(),
                clearButton
            });

            this.Controls.Add(toolStrip);
        }

        private void CreateMainPanel()
        {
            mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Padding = new Padding(10);

            CreateInputControls();
            CreateResultsControls();

            this.Controls.Add(mainPanel);
        }

        private void CreateInputControls()
        {
            inputGroupBox = new GroupBox();
            inputGroupBox.Text = "Tra cứu mã số thuế";
            inputGroupBox.Dock = DockStyle.Top;
            inputGroupBox.Height = 180;
            inputGroupBox.Padding = new Padding(10);

            // Radio buttons
            singleTaxCodeRadio = new RadioButton();
            singleTaxCodeRadio.Text = "Tra cứu mã số thuế đơn lẻ";
            singleTaxCodeRadio.Location = new Point(20, 30);
            singleTaxCodeRadio.Size = new Size(200, 25);
            singleTaxCodeRadio.Checked = true;
            singleTaxCodeRadio.CheckedChanged += RadioButton_CheckedChanged;

            multipleTaxCodeRadio = new RadioButton();
            multipleTaxCodeRadio.Text = "Tra cứu nhiều mã số thuế từ file";
            multipleTaxCodeRadio.Location = new Point(20, 90);
            multipleTaxCodeRadio.Size = new Size(200, 25);
            multipleTaxCodeRadio.CheckedChanged += RadioButton_CheckedChanged;

            // Single tax code input
            singleTaxCodeTextBox = new TextBox();
            singleTaxCodeTextBox.Location = new Point(240, 30);
            singleTaxCodeTextBox.Size = new Size(200, 25);
            singleTaxCodeTextBox.PlaceholderText = "Nhập mã số thuế...";

            historyComboBox = new ComboBox();
            historyComboBox.Location = new Point(450, 30);
            historyComboBox.Size = new Size(200, 25);
            historyComboBox.DropDownStyle = ComboBoxStyle.DropDown;
            historyComboBox.Text = "Lịch sử tra cứu...";
            historyComboBox.SelectedIndexChanged += HistoryComboBox_SelectedIndexChanged;

            // File input
            filePathTextBox = new TextBox();
            filePathTextBox.Location = new Point(240, 90);
            filePathTextBox.Size = new Size(300, 25);
            filePathTextBox.PlaceholderText = "Chọn file .txt, .xls, .xlsx...";
            filePathTextBox.Enabled = false;

            browseFileButton = new Button();
            browseFileButton.Text = "Chọn file...";
            browseFileButton.Location = new Point(550, 90);
            browseFileButton.Size = new Size(100, 25);
            browseFileButton.Enabled = false;
            browseFileButton.Click += BrowseFile_Click;

            // Action buttons
            searchButton = new Button();
            searchButton.Text = "Tra cứu";
            searchButton.Location = new Point(240, 130);
            searchButton.Size = new Size(100, 30);
            searchButton.BackColor = Color.FromArgb(0, 120, 215);
            searchButton.ForeColor = Color.White;
            searchButton.FlatStyle = FlatStyle.Flat;
            searchButton.Click += Search_Click;

            clearButton = new Button();
            clearButton.Text = "Xóa";
            clearButton.Location = new Point(350, 130);
            clearButton.Size = new Size(80, 30);
            clearButton.Click += Clear_Click;

            inputGroupBox.Controls.AddRange(new Control[] {
                singleTaxCodeRadio, multipleTaxCodeRadio,
                singleTaxCodeTextBox, historyComboBox,
                filePathTextBox, browseFileButton,
                searchButton, clearButton
            });

            mainPanel.Controls.Add(inputGroupBox);
        }

        private void CreateResultsControls()
        {
            resultsGroupBox = new GroupBox();
            resultsGroupBox.Text = "Kết quả tra cứu";
            resultsGroupBox.Dock = DockStyle.Fill;
            resultsGroupBox.Padding = new Padding(10);

            // DataGridView
            resultsDataGridView = new DataGridView();
            resultsDataGridView.Dock = DockStyle.Fill;
            resultsDataGridView.AllowUserToAddRows = false;
            resultsDataGridView.AllowUserToDeleteRows = false;
            resultsDataGridView.ReadOnly = true;
            resultsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            resultsDataGridView.MultiSelect = true;
            resultsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            resultsDataGridView.RowHeadersVisible = false;
            resultsDataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);

            // Context menu for right-click
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add("Xuất dòng được chọn (Excel)", null, ExportSelectedExcel_Click);
            contextMenu.Items.Add("Xuất dòng được chọn (JSON)", null, ExportSelectedJSON_Click);
            contextMenu.Items.Add("Xuất dòng được chọn (CSV)", null, ExportSelectedCSV_Click);
            contextMenu.Items.Add(new ToolStripSeparator());
            contextMenu.Items.Add("Sao chép mã số thuế", null, CopyTaxCode_Click);
            contextMenu.Items.Add("Sao chép tên công ty", null, CopyCompanyName_Click);
            resultsDataGridView.ContextMenuStrip = contextMenu;

            // Button panel
            resultsButtonPanel = new Panel();
            resultsButtonPanel.Dock = DockStyle.Bottom;
            resultsButtonPanel.Height = 50;
            resultsButtonPanel.Padding = new Padding(5);

            exportSelectedButton = new Button();
            exportSelectedButton.Text = "Xuất dòng được chọn";
            exportSelectedButton.Location = new Point(10, 10);
            exportSelectedButton.Size = new Size(150, 30);
            exportSelectedButton.Click += ExportSelected_Click;

            exportAllButton = new Button();
            exportAllButton.Text = "Xuất tất cả";
            exportAllButton.Location = new Point(170, 10);
            exportAllButton.Size = new Size(100, 30);
            exportAllButton.Click += ExportAll_Click;

            clearResultsButton = new Button();
            clearResultsButton.Text = "Xóa kết quả";
            clearResultsButton.Location = new Point(280, 10);
            clearResultsButton.Size = new Size(100, 30);
            clearResultsButton.Click += ClearResults_Click;

            resultsButtonPanel.Controls.AddRange(new Control[] {
                exportSelectedButton, exportAllButton, clearResultsButton
            });

            resultsGroupBox.Controls.Add(resultsDataGridView);
            resultsGroupBox.Controls.Add(resultsButtonPanel);

            mainPanel.Controls.Add(resultsGroupBox);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            
            statusLabel = new ToolStripStatusLabel();
            statusLabel.Text = "Sẵn sàng";
            statusLabel.Spring = true;
            statusLabel.TextAlign = ContentAlignment.MiddleLeft;

            progressBar = new ToolStripProgressBar();
            progressBar.Visible = false;

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, progressBar });
            this.Controls.Add(statusStrip);
        }

        // Event Handlers
        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            bool isSingle = singleTaxCodeRadio.Checked;

            singleTaxCodeTextBox.Enabled = isSingle;
            historyComboBox.Enabled = isSingle;
            filePathTextBox.Enabled = !isSingle;
            browseFileButton.Enabled = !isSingle;
        }

        private void HistoryComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (historyComboBox.SelectedItem != null)
            {
                singleTaxCodeTextBox.Text = historyComboBox.SelectedItem.ToString();
            }
        }

        private void BrowseFile_Click(object sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "Text files (*.txt)|*.txt|Excel files (*.xls;*.xlsx)|*.xls;*.xlsx|All files (*.*)|*.*";
            openFileDialog.FilterIndex = 1;
            openFileDialog.RestoreDirectory = true;

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                filePathTextBox.Text = openFileDialog.FileName;
            }
        }

        private async void Search_Click(object sender, EventArgs e)
        {
            try
            {
                searchButton.Enabled = false;
                progressBar.Visible = true;
                statusLabel.Text = "Đang tra cứu...";

                if (singleTaxCodeRadio.Checked)
                {
                    await SearchSingleTaxCode();
                }
                else
                {
                    await SearchMultipleTaxCodes();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                searchButton.Enabled = true;
                progressBar.Visible = false;
                statusLabel.Text = $"Hoàn thành. Tìm thấy {_companies.Count} kết quả.";
            }
        }

        private async Task SearchSingleTaxCode()
        {
            string taxCode = singleTaxCodeTextBox.Text.Trim();

            if (string.IsNullOrEmpty(taxCode))
            {
                MessageBox.Show("Vui lòng nhập mã số thuế!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!_taxService.IsValidTaxCode(taxCode))
            {
                MessageBox.Show("Mã số thuế không hợp lệ!\nMã số thuế phải có 10 hoặc 13 ký tự (bao gồm dấu gạch ngang)\nVí dụ: 0123456789 hoặc 0123456789-001",
                    "Mã số thuế không hợp lệ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var company = await _taxService.GetCompanyInfo(taxCode);
            if (company != null && !string.IsNullOrEmpty(company.TenCongTy))
            {
                _companies.Clear();
                _companies.Add(company);
                UpdateResultsGrid();
                AddToHistory(taxCode);
            }
            else
            {
                MessageBox.Show("Không tìm thấy thông tin cho mã số thuế này!", "Không tìm thấy", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async Task SearchMultipleTaxCodes()
        {
            string filePath = filePathTextBox.Text.Trim();

            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
            {
                MessageBox.Show("Vui lòng chọn file hợp lệ!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var taxCodes = await _taxService.ReadTaxCodesFromFile(filePath);
            if (taxCodes.Count == 0)
            {
                MessageBox.Show("Không tìm thấy mã số thuế nào trong file!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            _companies.Clear();
            progressBar.Maximum = taxCodes.Count;
            progressBar.Value = 0;

            for (int i = 0; i < taxCodes.Count; i++)
            {
                var taxCode = taxCodes[i];
                statusLabel.Text = $"Đang tra cứu {taxCode} ({i + 1}/{taxCodes.Count})...";
                progressBar.Value = i + 1;

                if (_taxService.IsValidTaxCode(taxCode))
                {
                    try
                    {
                        var company = await _taxService.GetCompanyInfo(taxCode);
                        if (company != null && !string.IsNullOrEmpty(company.TenCongTy))
                        {
                            _companies.Add(company);
                            UpdateResultsGrid();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue
                        Console.WriteLine($"Error processing {taxCode}: {ex.Message}");
                    }
                }

                // Delay to avoid overwhelming the server
                await Task.Delay(1000);
            }

            UpdateResultsGrid();
        }

        private void Clear_Click(object sender, EventArgs e)
        {
            singleTaxCodeTextBox.Clear();
            filePathTextBox.Clear();
        }

        private void UpdateResultsGrid()
        {
            if (resultsDataGridView.DataSource == null)
            {
                SetupDataGridView();
            }

            var bindingSource = new BindingSource();
            bindingSource.DataSource = _companies;
            resultsDataGridView.DataSource = bindingSource;
        }

        private void SetupDataGridView()
        {
            resultsDataGridView.Columns.Clear();

            resultsDataGridView.Columns.Add("MaSoThue", "Mã số thuế");
            resultsDataGridView.Columns.Add("TenCongTy", "Tên công ty");
            resultsDataGridView.Columns.Add("DiaChi", "Địa chỉ");
            resultsDataGridView.Columns.Add("NguoiDaiDien", "Người đại diện");
            resultsDataGridView.Columns.Add("NgayHoatDong", "Ngày hoạt động");
            resultsDataGridView.Columns.Add("QuanLyBoi", "Quản lý bởi");
            resultsDataGridView.Columns.Add("LoaiHinhDN", "Loại hình DN");
            resultsDataGridView.Columns.Add("TinhTrang", "Tình trạng");

            // Set column widths
            resultsDataGridView.Columns["MaSoThue"].Width = 120;
            resultsDataGridView.Columns["TenCongTy"].Width = 250;
            resultsDataGridView.Columns["DiaChi"].Width = 200;
            resultsDataGridView.Columns["NguoiDaiDien"].Width = 150;
            resultsDataGridView.Columns["NgayHoatDong"].Width = 100;
            resultsDataGridView.Columns["QuanLyBoi"].Width = 150;
            resultsDataGridView.Columns["LoaiHinhDN"].Width = 120;
            resultsDataGridView.Columns["TinhTrang"].Width = 200;

            resultsDataGridView.DataSource = _companies;
        }

        private void AddToHistory(string taxCode)
        {
            if (!_searchHistory.Contains(taxCode))
            {
                _searchHistory.Insert(0, taxCode);
                if (_searchHistory.Count > 20) // Keep only last 20 searches
                {
                    _searchHistory.RemoveAt(_searchHistory.Count - 1);
                }

                historyComboBox.Items.Clear();
                historyComboBox.Items.AddRange(_searchHistory.ToArray());
                SaveSearchHistory();
            }
        }

        private void LoadSearchHistory()
        {
            try
            {
                string historyFile = Path.Combine(Application.StartupPath, "search_history.json");
                if (File.Exists(historyFile))
                {
                    string json = File.ReadAllText(historyFile);
                    _searchHistory = JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>();
                    historyComboBox.Items.AddRange(_searchHistory.ToArray());
                }
            }
            catch
            {
                _searchHistory = new List<string>();
            }
        }

        private void SaveSearchHistory()
        {
            try
            {
                string historyFile = Path.Combine(Application.StartupPath, "search_history.json");
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
                };
                string json = JsonSerializer.Serialize(_searchHistory, options);
                File.WriteAllText(historyFile, json);
            }
            catch
            {
                // Ignore save errors
            }
        }

        // Export and Menu Event Handlers
        private void OpenFile_Click(object sender, EventArgs e)
        {
            multipleTaxCodeRadio.Checked = true;
            BrowseFile_Click(sender, e);
        }

        private void ExportSelected_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn ít nhất một dòng để xuất!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            ShowExportDialog(selectedCompanies);
        }

        private void ExportAll_Click(object sender, EventArgs e)
        {
            if (_companies.Count == 0)
            {
                MessageBox.Show("Không có dữ liệu để xuất!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            ShowExportDialog(_companies);
        }

        private void ShowExportDialog(List<CompanyInfo> companies)
        {
            using var dialog = new Form();
            dialog.Text = "Chọn định dạng xuất";
            dialog.Size = new Size(300, 200);
            dialog.StartPosition = FormStartPosition.CenterParent;
            dialog.FormBorderStyle = FormBorderStyle.FixedDialog;
            dialog.MaximizeBox = false;
            dialog.MinimizeBox = false;

            var excelButton = new Button { Text = "Excel (.xlsx)", Location = new Point(50, 30), Size = new Size(200, 30) };
            var jsonButton = new Button { Text = "JSON", Location = new Point(50, 70), Size = new Size(200, 30) };
            var csvButton = new Button { Text = "CSV", Location = new Point(50, 110), Size = new Size(200, 30) };

            excelButton.Click += (s, e) => { dialog.DialogResult = DialogResult.OK; dialog.Tag = "excel"; dialog.Close(); };
            jsonButton.Click += (s, e) => { dialog.DialogResult = DialogResult.OK; dialog.Tag = "json"; dialog.Close(); };
            csvButton.Click += (s, e) => { dialog.DialogResult = DialogResult.OK; dialog.Tag = "csv"; dialog.Close(); };

            dialog.Controls.AddRange(new Control[] { excelButton, jsonButton, csvButton });

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                string format = dialog.Tag.ToString();
                ExportData(companies, format);
            }
        }

        private async void ExportData(List<CompanyInfo> companies, string format)
        {
            try
            {
                using var saveFileDialog = new SaveFileDialog();
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

                switch (format)
                {
                    case "excel":
                        saveFileDialog.Filter = "Excel files (*.xlsx)|*.xlsx";
                        saveFileDialog.FileName = $"KetQua_MST_{timestamp}.xlsx";
                        break;
                    case "json":
                        saveFileDialog.Filter = "JSON files (*.json)|*.json";
                        saveFileDialog.FileName = $"KetQua_MST_{timestamp}.json";
                        break;
                    case "csv":
                        saveFileDialog.Filter = "CSV files (*.csv)|*.csv";
                        saveFileDialog.FileName = $"KetQua_MST_{timestamp}.csv";
                        break;
                }

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    statusLabel.Text = "Đang xuất file...";

                    switch (format)
                    {
                        case "excel":
                            await _taxService.ExportToExcel(companies, saveFileDialog.FileName);
                            break;
                        case "json":
                            await _taxService.ExportToJSON(companies, saveFileDialog.FileName);
                            break;
                        case "csv":
                            await _taxService.ExportToCSV(companies, saveFileDialog.FileName);
                            break;
                    }

                    statusLabel.Text = "Xuất file thành công!";
                    MessageBox.Show($"Đã xuất file thành công:\n{saveFileDialog.FileName}", "Thành công", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi xuất file: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "Lỗi khi xuất file";
            }
        }

        private List<CompanyInfo> GetSelectedCompanies()
        {
            var selectedCompanies = new List<CompanyInfo>();
            foreach (DataGridViewRow row in resultsDataGridView.SelectedRows)
            {
                if (row.Index < _companies.Count)
                {
                    selectedCompanies.Add(_companies[row.Index]);
                }
            }
            return selectedCompanies;
        }

        // Context menu handlers
        private void ExportSelectedExcel_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
                ExportData(selectedCompanies, "excel");
        }

        private void ExportSelectedJSON_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
                ExportData(selectedCompanies, "json");
        }

        private void ExportSelectedCSV_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
                ExportData(selectedCompanies, "csv");
        }

        private void CopyTaxCode_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
            {
                string taxCodes = string.Join("\n", selectedCompanies.Select(c => c.MaSoThue));
                Clipboard.SetText(taxCodes);
                statusLabel.Text = "Đã sao chép mã số thuế";
            }
        }

        private void CopyCompanyName_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
            {
                string companyNames = string.Join("\n", selectedCompanies.Select(c => c.TenCongTy));
                Clipboard.SetText(companyNames);
                statusLabel.Text = "Đã sao chép tên công ty";
            }
        }

        // Menu handlers
        private void ExportExcel_Click(object sender, EventArgs e)
        {
            ExportData(_companies, "excel");
        }

        private void ExportJSON_Click(object sender, EventArgs e)
        {
            ExportData(_companies, "json");
        }

        private void ExportCSV_Click(object sender, EventArgs e)
        {
            ExportData(_companies, "csv");
        }

        private void ClearHistory_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("Bạn có chắc chắn muốn xóa lịch sử tra cứu?", "Xác nhận", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                _searchHistory.Clear();
                historyComboBox.Items.Clear();
                SaveSearchHistory();
                statusLabel.Text = "Đã xóa lịch sử tra cứu";
            }
        }

        private void ClearResults_Click(object sender, EventArgs e)
        {
            _companies.Clear();
            UpdateResultsGrid();
            statusLabel.Text = "Đã xóa kết quả tra cứu";
        }

        private void ShowHelp_Click(object sender, EventArgs e)
        {
            string helpText = @"HƯỚNG DẪN SỬ DỤNG

1. TRA CỨU ĐỐN LẺ:
   - Chọn 'Tra cứu mã số thuế đơn lẻ'
   - Nhập mã số thuế (10 hoặc 13 ký tự)
   - Nhấn 'Tra cứu'

2. TRA CỨU NHIỀU:
   - Chọn 'Tra cứu nhiều mã số thuế từ file'
   - Chọn file .txt, .xls hoặc .xlsx
   - File phải chứa mỗi mã số thuế trên một dòng
   - Nhấn 'Tra cứu'

3. XUẤT KẾT QUẢ:
   - Chọn dòng cần xuất trong bảng kết quả
   - Nhấn chuột phải để hiện menu
   - Chọn định dạng xuất: Excel, JSON hoặc CSV

4. LỊCH SỬ:
   - Các mã số thuế đã tra cứu sẽ được lưu lại
   - Chọn từ dropdown để tra cứu lại

ĐỊNH DẠNG MÃ SỐ THUẾ:
- 10 chữ số: 0123456789
- 13 ký tự: 0123456789-001";

            MessageBox.Show(helpText, "Hướng dẫn sử dụng", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowAbout_Click(object sender, EventArgs e)
        {
            string aboutText = @"CHƯƠNG TRÌNH TRA CỨU MÃ SỐ THUẾ

Phiên bản: 2.0
Ngày phát hành: 2025

Tính năng:
✓ Tra cứu mã số thuế đơn lẻ
✓ Tra cứu nhiều mã số thuế từ file
✓ Xuất kết quả Excel, JSON, CSV
✓ Lưu lịch sử tra cứu
✓ Giao diện Windows Forms thân thiện

Hỗ trợ: <EMAIL>";

            MessageBox.Show(aboutText, "Về chương trình", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
