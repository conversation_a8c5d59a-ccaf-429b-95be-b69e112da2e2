using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Text.Json;
using System.Text.Encodings.Web;
using System.Text.Unicode;

namespace KiemtraMST
{
    public partial class MainForm : Form
    {
        private List<CompanyInfo> _companies = new List<CompanyInfo>();
        private List<string> _searchHistory = new List<string>();
        private TaxService _taxService = new TaxService();

        public MainForm()
        {
            InitializeComponent();
            LoadSearchHistory();

            // Force layout refresh
            this.Load += MainForm_Load;
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // Setup grid columns
            SetupDataGridView();
            LoadSearchHistory();
            statusLabel.Text = "Sẵn sàng tra cứu mã số thuế";
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "Chương trình tra cứu mã số thuế";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(900, 600);
            this.Icon = SystemIcons.Application;
            this.WindowState = FormWindowState.Normal;

            // Create controls - simple layout
            CreateStatusStrip();    // Bottom first
            CreateMainPanel();      // Fill remaining

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private Panel mainPanel;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private ToolStripProgressBar progressBar;

        // Input controls
        private GroupBox inputGroupBox;
        private RadioButton singleTaxCodeRadio;
        private RadioButton multipleTaxCodeRadio;
        private TextBox singleTaxCodeTextBox;
        private ComboBox historyComboBox;
        private TextBox filePathTextBox;
        private Button browseFileButton;
        private Button searchButton;
        private Button clearButton;

        // Results controls
        private GroupBox resultsGroupBox;
        private DataGridView resultsDataGridView;
        private Panel resultsButtonPanel;
        private Button exportSelectedButton;
        private Button exportAllButton;
        private Button clearResultsButton;



        private void CreateMainPanel()
        {
            mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Padding = new Padding(5);

            CreateInputControls();
            CreateResultsControls();

            this.Controls.Add(mainPanel);
        }

        private void CreateInputControls()
        {
            inputGroupBox = new GroupBox();
            inputGroupBox.Text = "Tra cứu mã số thuế";
            inputGroupBox.Dock = DockStyle.Top;
            inputGroupBox.Height = 120;
            inputGroupBox.Padding = new Padding(10);

            // Radio buttons
            singleTaxCodeRadio = new RadioButton();
            singleTaxCodeRadio.Text = "Tra cứu đơn lẻ";
            singleTaxCodeRadio.Location = new Point(15, 25);
            singleTaxCodeRadio.Size = new Size(120, 20);
            singleTaxCodeRadio.Checked = true;
            singleTaxCodeRadio.CheckedChanged += RadioButton_CheckedChanged;

            multipleTaxCodeRadio = new RadioButton();
            multipleTaxCodeRadio.Text = "Tra cứu từ file";
            multipleTaxCodeRadio.Location = new Point(15, 65);
            multipleTaxCodeRadio.Size = new Size(120, 20);
            multipleTaxCodeRadio.CheckedChanged += RadioButton_CheckedChanged;

            // Single tax code input
            singleTaxCodeTextBox = new TextBox();
            singleTaxCodeTextBox.Location = new Point(150, 25);
            singleTaxCodeTextBox.Size = new Size(120, 20);
            singleTaxCodeTextBox.PlaceholderText = "Nhập mã số thuế...";

            historyComboBox = new ComboBox();
            historyComboBox.Location = new Point(280, 25);
            historyComboBox.Size = new Size(120, 20);
            historyComboBox.DropDownStyle = ComboBoxStyle.DropDown;
            historyComboBox.Text = "Lịch sử...";
            historyComboBox.SelectedIndexChanged += HistoryComboBox_SelectedIndexChanged;

            // File input
            filePathTextBox = new TextBox();
            filePathTextBox.Location = new Point(150, 65);
            filePathTextBox.Size = new Size(200, 20);
            filePathTextBox.PlaceholderText = "Chọn file .txt, .xls, .xlsx...";
            filePathTextBox.Enabled = false;

            browseFileButton = new Button();
            browseFileButton.Text = "Chọn file";
            browseFileButton.Location = new Point(360, 65);
            browseFileButton.Size = new Size(70, 22);
            browseFileButton.Enabled = false;
            browseFileButton.Click += BrowseFile_Click;

            // Action buttons
            searchButton = new Button();
            searchButton.Text = "Tra cứu";
            searchButton.Location = new Point(450, 25);
            searchButton.Size = new Size(70, 25);
            searchButton.BackColor = Color.FromArgb(0, 120, 215);
            searchButton.ForeColor = Color.White;
            searchButton.FlatStyle = FlatStyle.Flat;
            searchButton.Click += Search_Click;

            clearButton = new Button();
            clearButton.Text = "Xóa";
            clearButton.Location = new Point(530, 25);
            clearButton.Size = new Size(50, 25);
            clearButton.Click += Clear_Click;

            inputGroupBox.Controls.AddRange(new Control[] {
                singleTaxCodeRadio, multipleTaxCodeRadio,
                singleTaxCodeTextBox, historyComboBox,
                filePathTextBox, browseFileButton,
                searchButton, clearButton
            });

            mainPanel.Controls.Add(inputGroupBox);
        }

        private void CreateResultsControls()
        {
            resultsGroupBox = new GroupBox();
            resultsGroupBox.Text = "Kết quả tra cứu";
            resultsGroupBox.Dock = DockStyle.Fill;
            resultsGroupBox.Padding = new Padding(5);

            // Button panel at top
            resultsButtonPanel = new Panel();
            resultsButtonPanel.Dock = DockStyle.Top;
            resultsButtonPanel.Height = 35;
            resultsButtonPanel.BackColor = Color.FromArgb(245, 245, 245);

            exportSelectedButton = new Button();
            exportSelectedButton.Text = "Xuất được chọn";
            exportSelectedButton.Location = new Point(5, 5);
            exportSelectedButton.Size = new Size(100, 25);
            exportSelectedButton.Click += ExportSelected_Click;

            exportAllButton = new Button();
            exportAllButton.Text = "Xuất tất cả";
            exportAllButton.Location = new Point(110, 5);
            exportAllButton.Size = new Size(70, 25);
            exportAllButton.Click += ExportAll_Click;

            clearResultsButton = new Button();
            clearResultsButton.Text = "Xóa kết quả";
            clearResultsButton.Location = new Point(185, 5);
            clearResultsButton.Size = new Size(70, 25);
            clearResultsButton.Click += ClearResults_Click;

            resultsButtonPanel.Controls.AddRange(new Control[] {
                exportSelectedButton, exportAllButton, clearResultsButton
            });

            // DataGridView fills remaining space
            resultsDataGridView = new DataGridView();
            resultsDataGridView.Dock = DockStyle.Fill;
            resultsDataGridView.AllowUserToAddRows = false;
            resultsDataGridView.AllowUserToDeleteRows = false;
            resultsDataGridView.ReadOnly = true;
            resultsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            resultsDataGridView.MultiSelect = true;
            resultsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            resultsDataGridView.RowHeadersVisible = false;
            resultsDataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
            resultsDataGridView.BorderStyle = BorderStyle.Fixed3D;
            resultsDataGridView.BackgroundColor = Color.White;
            resultsDataGridView.GridColor = Color.LightGray;
            resultsDataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(51, 153, 255);
            resultsDataGridView.DefaultCellStyle.SelectionForeColor = Color.White;

            // Context menu for right-click
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add("Xuất Excel", null, ExportSelectedExcel_Click);
            contextMenu.Items.Add("Xuất JSON", null, ExportSelectedJSON_Click);
            contextMenu.Items.Add("Xuất CSV", null, ExportSelectedCSV_Click);
            contextMenu.Items.Add(new ToolStripSeparator());
            contextMenu.Items.Add("Copy mã số thuế", null, CopyTaxCode_Click);
            contextMenu.Items.Add("Copy tên công ty", null, CopyCompanyName_Click);
            resultsDataGridView.ContextMenuStrip = contextMenu;

            // Add controls in correct order
            resultsGroupBox.Controls.Add(resultsDataGridView);  // Grid fills remaining space
            resultsGroupBox.Controls.Add(resultsButtonPanel);   // Panel docks to top

            mainPanel.Controls.Add(resultsGroupBox);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusStrip.Dock = DockStyle.Bottom;

            statusLabel = new ToolStripStatusLabel();
            statusLabel.Text = "Sẵn sàng";
            statusLabel.Spring = true;
            statusLabel.TextAlign = ContentAlignment.MiddleLeft;

            progressBar = new ToolStripProgressBar();
            progressBar.Visible = false;
            progressBar.Size = new Size(200, 16);

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, progressBar });
            this.Controls.Add(statusStrip);
        }

        // Event Handlers
        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            bool isSingle = singleTaxCodeRadio.Checked;

            singleTaxCodeTextBox.Enabled = isSingle;
            historyComboBox.Enabled = isSingle;
            filePathTextBox.Enabled = !isSingle;
            browseFileButton.Enabled = !isSingle;
        }

        private void HistoryComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (historyComboBox.SelectedItem != null)
            {
                singleTaxCodeTextBox.Text = historyComboBox.SelectedItem.ToString();
            }
        }

        private void BrowseFile_Click(object sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "Text files (*.txt)|*.txt|Excel files (*.xls;*.xlsx)|*.xls;*.xlsx|All files (*.*)|*.*";
            openFileDialog.FilterIndex = 1;
            openFileDialog.RestoreDirectory = true;

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                filePathTextBox.Text = openFileDialog.FileName;
            }
        }

        private async void Search_Click(object sender, EventArgs e)
        {
            try
            {
                searchButton.Enabled = false;
                progressBar.Visible = true;
                statusLabel.Text = "Đang tra cứu...";

                if (singleTaxCodeRadio.Checked)
                {
                    await SearchSingleTaxCode();
                }
                else
                {
                    await SearchMultipleTaxCodes();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                searchButton.Enabled = true;
                progressBar.Visible = false;
                statusLabel.Text = $"Hoàn thành. Tìm thấy {_companies.Count} kết quả.";
            }
        }

        private async Task SearchSingleTaxCode()
        {
            string taxCode = singleTaxCodeTextBox.Text.Trim();

            if (string.IsNullOrEmpty(taxCode))
            {
                MessageBox.Show("Vui lòng nhập mã số thuế!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!_taxService.IsValidTaxCode(taxCode))
            {
                MessageBox.Show("Mã số thuế không hợp lệ!\nMã số thuế phải có 10 hoặc 13 ký tự (bao gồm dấu gạch ngang)\nVí dụ: 0123456789 hoặc 0123456789-001",
                    "Mã số thuế không hợp lệ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                searchButton.Enabled = false;
                statusLabel.Text = $"Đang tra cứu MST: {taxCode}...";
                progressBar.Visible = true;

                // DEBUG: Kiểm tra trước khi gọi API
                MessageBox.Show($"Bắt đầu tra cứu MST: {taxCode}", "Debug", MessageBoxButtons.OK, MessageBoxIcon.Information);

                var company = await GetCompanyInfoDirect(taxCode);

                // DEBUG: Kiểm tra kết quả trả về
                if (company == null)
                {
                    MessageBox.Show("API trả về NULL", "Debug", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                MessageBox.Show($"Company Info:\n- MST: '{company.MaSoThue}'\n- Tên: '{company.TenCongTy}'\n- Địa chỉ: '{company.DiaChi}'\n- Đại diện: '{company.NguoiDaiDien}'",
                    "Debug Company", MessageBoxButtons.OK, MessageBoxIcon.Information);

                if (company != null && !string.IsNullOrEmpty(company.TenCongTy))
                {
                    _companies.Clear();
                    _companies.Add(company);

                    MessageBox.Show($"Added to _companies. Count: {_companies.Count}", "Debug List", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    UpdateResultsGrid();
                    AddToHistory(taxCode);
                    statusLabel.Text = $"Tìm thấy thông tin cho mã số thuế {taxCode}";
                }
                else
                {
                    MessageBox.Show("Không tìm thấy thông tin hoặc tên công ty trống!", "Không tìm thấy", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    statusLabel.Text = $"Không tìm thấy thông tin cho mã số thuế {taxCode}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tra cứu: {ex.Message}\n\nStack trace:\n{ex.StackTrace}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "Lỗi khi tra cứu";
            }
            finally
            {
                searchButton.Enabled = true;
                progressBar.Visible = false;
            }
        }

        private async Task SearchMultipleTaxCodes()
        {
            string filePath = filePathTextBox.Text.Trim();

            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
            {
                MessageBox.Show("Vui lòng chọn file hợp lệ!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                searchButton.Enabled = false;
                progressBar.Visible = true;

                var taxCodes = await ReadTaxCodesFromFile(filePath);
                if (taxCodes.Count == 0)
                {
                    MessageBox.Show("Không tìm thấy mã số thuế nào trong file!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _companies.Clear();
                progressBar.Maximum = taxCodes.Count;
                progressBar.Value = 0;

                int successCount = 0;
                int errorCount = 0;

                for (int i = 0; i < taxCodes.Count; i++)
                {
                    var taxCode = taxCodes[i].Trim();
                    statusLabel.Text = $"Đang tra cứu {taxCode} ({i + 1}/{taxCodes.Count})...";
                    progressBar.Value = i + 1;

                    if (_taxService.IsValidTaxCode(taxCode))
                    {
                        try
                        {
                            var company = await GetCompanyInfoDirect(taxCode);
                            if (company != null && !string.IsNullOrEmpty(company.TenCongTy))
                            {
                                _companies.Add(company);
                                UpdateResultsGrid();
                                successCount++;
                            }
                            else
                            {
                                errorCount++;
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log error but continue
                            Console.WriteLine($"Error processing {taxCode}: {ex.Message}");
                            errorCount++;
                        }
                    }
                    else
                    {
                        errorCount++;
                    }

                    // Delay to avoid overwhelming the server
                    await Task.Delay(1000);
                }

                UpdateResultsGrid();
                statusLabel.Text = $"Hoàn thành! Thành công: {successCount}, Lỗi: {errorCount}, Tổng: {taxCodes.Count}";
                MessageBox.Show($"Tra cứu hoàn thành!\n\nThành công: {successCount}\nLỗi: {errorCount}\nTổng cộng: {taxCodes.Count}",
                    "Kết quả", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            finally
            {
                searchButton.Enabled = true;
                progressBar.Visible = false;
            }
        }

        private void Clear_Click(object sender, EventArgs e)
        {
            singleTaxCodeTextBox.Clear();
            filePathTextBox.Clear();
        }

        private void UpdateResultsGrid()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(UpdateResultsGrid));
                return;
            }

            try
            {
                // DEBUG: Kiểm tra dữ liệu trước khi update
                MessageBox.Show($"UpdateResultsGrid called with {_companies.Count} companies", "Debug UpdateGrid", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Clear hoàn toàn
                resultsDataGridView.SuspendLayout();
                resultsDataGridView.DataSource = null;
                resultsDataGridView.Rows.Clear();
                resultsDataGridView.Columns.Clear();

                // Setup lại columns
                SetupDataGridView();

                if (_companies.Count > 0)
                {
                    // DEBUG: Hiển thị thông tin company đầu tiên
                    var firstCompany = _companies[0];
                    MessageBox.Show($"First Company:\n- MST: '{firstCompany.MaSoThue}'\n- Tên: '{firstCompany.TenCongTy}'\n- Địa chỉ: '{firstCompany.DiaChi}'",
                        "Debug First Company", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Thử cách đơn giản nhất - Add từng row
                    foreach (var company in _companies)
                    {
                        int rowIndex = resultsDataGridView.Rows.Add();
                        var row = resultsDataGridView.Rows[rowIndex];

                        row.Cells[0].Value = company.MaSoThue ?? "";
                        row.Cells[1].Value = company.TenCongTy ?? "";
                        row.Cells[2].Value = company.DiaChi ?? "";
                        row.Cells[3].Value = company.NguoiDaiDien ?? "";
                        row.Cells[4].Value = company.NgayHoatDong ?? "";
                        row.Cells[5].Value = company.QuanLyBoi ?? "";
                        row.Cells[6].Value = company.LoaiHinhDN ?? "";
                        row.Cells[7].Value = company.TinhTrang ?? "";
                    }

                    MessageBox.Show($"Grid now has {resultsDataGridView.Rows.Count} rows", "Debug Final Count", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                resultsDataGridView.ResumeLayout();
                resultsDataGridView.Refresh();
                resultsDataGridView.Update();
                Application.DoEvents();

                // Update status
                statusLabel.Text = $"Hiển thị {_companies.Count} kết quả";
            }
            catch (Exception ex)
            {
                resultsDataGridView.ResumeLayout();
                MessageBox.Show($"Lỗi cập nhật grid: {ex.Message}\n{ex.StackTrace}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupDataGridView()
        {
            resultsDataGridView.Columns.Clear();
            resultsDataGridView.Rows.Clear();
            resultsDataGridView.AutoGenerateColumns = false;
            resultsDataGridView.DataSource = null;

            // Cấu hình chung cho DataGridView - CHO PHÉP ADD ROWS
            resultsDataGridView.AllowUserToAddRows = false; // Không cho user add, nhưng code có thể add
            resultsDataGridView.AllowUserToDeleteRows = false;
            resultsDataGridView.ReadOnly = true;
            resultsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            resultsDataGridView.MultiSelect = true;
            resultsDataGridView.RowHeadersVisible = false;
            resultsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None; // Tắt auto size
            resultsDataGridView.ScrollBars = ScrollBars.Both;
            resultsDataGridView.DefaultCellStyle.WrapMode = DataGridViewTriState.False;
            resultsDataGridView.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.None;
            resultsDataGridView.RowTemplate.Height = 28;

            // Setup columns đơn giản
            resultsDataGridView.Columns.Add("MaSoThue", "Mã số thuế");
            resultsDataGridView.Columns.Add("TenCongTy", "Tên công ty");
            resultsDataGridView.Columns.Add("DiaChi", "Địa chỉ");
            resultsDataGridView.Columns.Add("NguoiDaiDien", "Người đại diện");
            resultsDataGridView.Columns.Add("NgayHoatDong", "Ngày hoạt động");
            resultsDataGridView.Columns.Add("QuanLyBoi", "Quản lý bởi");
            resultsDataGridView.Columns.Add("LoaiHinhDN", "Loại hình DN");
            resultsDataGridView.Columns.Add("TinhTrang", "Tình trạng");

            // Set width cho các cột
            resultsDataGridView.Columns[0].Width = 120; // Mã số thuế
            resultsDataGridView.Columns[1].Width = 300; // Tên công ty
            resultsDataGridView.Columns[2].Width = 250; // Địa chỉ
            resultsDataGridView.Columns[3].Width = 150; // Người đại diện
            resultsDataGridView.Columns[4].Width = 120; // Ngày hoạt động
            resultsDataGridView.Columns[5].Width = 180; // Quản lý bởi
            resultsDataGridView.Columns[6].Width = 130; // Loại hình DN
            resultsDataGridView.Columns[7].Width = 200; // Tình trạng

            // DEBUG: Kiểm tra columns đã được tạo
            MessageBox.Show($"SetupDataGridView completed. Columns count: {resultsDataGridView.Columns.Count}", "Debug Setup", MessageBoxButtons.OK, MessageBoxIcon.Information);

            // Cấu hình header style
            resultsDataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(70, 130, 180);
            resultsDataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            resultsDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9, FontStyle.Bold);
            resultsDataGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            resultsDataGridView.ColumnHeadersHeight = 30;
            resultsDataGridView.EnableHeadersVisualStyles = false;

            // Cấu hình alternating row colors
            resultsDataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(240, 248, 255);
            resultsDataGridView.DefaultCellStyle.BackColor = Color.White;
            resultsDataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(51, 122, 183);
            resultsDataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            resultsDataGridView.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            resultsDataGridView.RowTemplate.Height = 28;

            // Border style
            resultsDataGridView.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            resultsDataGridView.GridColor = Color.LightGray;
        }

        private void AddToHistory(string taxCode)
        {
            if (!_searchHistory.Contains(taxCode))
            {
                _searchHistory.Insert(0, taxCode);
                if (_searchHistory.Count > 20) // Keep only last 20 searches
                {
                    _searchHistory.RemoveAt(_searchHistory.Count - 1);
                }

                historyComboBox.Items.Clear();
                historyComboBox.Items.AddRange(_searchHistory.ToArray());
                SaveSearchHistory();
            }
        }

        private void LoadSearchHistory()
        {
            try
            {
                string historyFile = Path.Combine(Application.StartupPath, "search_history.json");
                if (File.Exists(historyFile))
                {
                    string json = File.ReadAllText(historyFile);
                    _searchHistory = JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>();
                    historyComboBox.Items.AddRange(_searchHistory.ToArray());
                }
            }
            catch
            {
                _searchHistory = new List<string>();
            }
        }

        private void SaveSearchHistory()
        {
            try
            {
                string historyFile = Path.Combine(Application.StartupPath, "search_history.json");
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
                };
                string json = JsonSerializer.Serialize(_searchHistory, options);
                File.WriteAllText(historyFile, json);
            }
            catch
            {
                // Ignore save errors
            }
        }

        // Export Event Handlers

        private void ExportSelected_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn ít nhất một dòng để xuất!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            ShowExportDialog(selectedCompanies);
        }

        private void ExportAll_Click(object sender, EventArgs e)
        {
            if (_companies.Count == 0)
            {
                MessageBox.Show("Không có dữ liệu để xuất!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            ShowExportDialog(_companies);
        }

        private void ShowExportDialog(List<CompanyInfo> companies)
        {
            using var dialog = new Form();
            dialog.Text = "Chọn định dạng xuất";
            dialog.Size = new Size(300, 200);
            dialog.StartPosition = FormStartPosition.CenterParent;
            dialog.FormBorderStyle = FormBorderStyle.FixedDialog;
            dialog.MaximizeBox = false;
            dialog.MinimizeBox = false;

            var excelButton = new Button { Text = "Excel (.xlsx)", Location = new Point(50, 30), Size = new Size(200, 30) };
            var jsonButton = new Button { Text = "JSON", Location = new Point(50, 70), Size = new Size(200, 30) };
            var csvButton = new Button { Text = "CSV", Location = new Point(50, 110), Size = new Size(200, 30) };

            excelButton.Click += (s, e) => { dialog.DialogResult = DialogResult.OK; dialog.Tag = "excel"; dialog.Close(); };
            jsonButton.Click += (s, e) => { dialog.DialogResult = DialogResult.OK; dialog.Tag = "json"; dialog.Close(); };
            csvButton.Click += (s, e) => { dialog.DialogResult = DialogResult.OK; dialog.Tag = "csv"; dialog.Close(); };

            dialog.Controls.AddRange(new Control[] { excelButton, jsonButton, csvButton });

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                string format = dialog.Tag.ToString();
                ExportData(companies, format);
            }
        }

        private async void ExportData(List<CompanyInfo> companies, string format)
        {
            try
            {
                using var saveFileDialog = new SaveFileDialog();
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

                switch (format)
                {
                    case "excel":
                        saveFileDialog.Filter = "Excel files (*.xlsx)|*.xlsx";
                        saveFileDialog.FileName = $"KetQua_MST_{timestamp}.xlsx";
                        break;
                    case "json":
                        saveFileDialog.Filter = "JSON files (*.json)|*.json";
                        saveFileDialog.FileName = $"KetQua_MST_{timestamp}.json";
                        break;
                    case "csv":
                        saveFileDialog.Filter = "CSV files (*.csv)|*.csv";
                        saveFileDialog.FileName = $"KetQua_MST_{timestamp}.csv";
                        break;
                }

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    statusLabel.Text = "Đang xuất file...";

                    switch (format)
                    {
                        case "excel":
                            await _taxService.ExportToExcel(companies, saveFileDialog.FileName);
                            break;
                        case "json":
                            await _taxService.ExportToJSON(companies, saveFileDialog.FileName);
                            break;
                        case "csv":
                            await _taxService.ExportToCSV(companies, saveFileDialog.FileName);
                            break;
                    }

                    statusLabel.Text = "Xuất file thành công!";
                    MessageBox.Show($"Đã xuất file thành công:\n{saveFileDialog.FileName}", "Thành công", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi xuất file: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "Lỗi khi xuất file";
            }
        }

        private List<CompanyInfo> GetSelectedCompanies()
        {
            var selectedCompanies = new List<CompanyInfo>();
            foreach (DataGridViewRow row in resultsDataGridView.SelectedRows)
            {
                if (row.Index < _companies.Count)
                {
                    selectedCompanies.Add(_companies[row.Index]);
                }
            }
            return selectedCompanies;
        }

        // Context menu handlers
        private void ExportSelectedExcel_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
                ExportData(selectedCompanies, "excel");
        }

        private void ExportSelectedJSON_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
                ExportData(selectedCompanies, "json");
        }

        private void ExportSelectedCSV_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
                ExportData(selectedCompanies, "csv");
        }

        private void CopyTaxCode_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
            {
                string taxCodes = string.Join("\n", selectedCompanies.Select(c => c.MaSoThue));
                Clipboard.SetText(taxCodes);
                statusLabel.Text = "Đã sao chép mã số thuế";
            }
        }

        private void CopyCompanyName_Click(object sender, EventArgs e)
        {
            var selectedCompanies = GetSelectedCompanies();
            if (selectedCompanies.Count > 0)
            {
                string companyNames = string.Join("\n", selectedCompanies.Select(c => c.TenCongTy));
                Clipboard.SetText(companyNames);
                statusLabel.Text = "Đã sao chép tên công ty";
            }
        }

        private void ClearResults_Click(object sender, EventArgs e)
        {
            _companies.Clear();
            UpdateResultsGrid();
            statusLabel.Text = "Đã xóa kết quả tra cứu";
        }

        // Method tra cứu trực tiếp sử dụng logic từ MainProgram đã test thành công
        private async Task<CompanyInfo?> GetCompanyInfoDirect(string taxCode)
        {
            try
            {
                string url = $"https://masothue.com/Search/?q={taxCode}&type=auto&token=&force-search=0";

                using var client = new HttpClient();
                client.DefaultRequestHeaders.Add("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
                client.DefaultRequestHeaders.Add("Accept-Language", "vi-VN,vi;q=0.9,en;q=0.8");

                string htmlContent = await client.GetStringAsync(url);
                var htmlDoc = new HtmlAgilityPack.HtmlDocument();
                htmlDoc.LoadHtml(htmlContent);

                var company = new CompanyInfo();

                // Lấy tên công ty từ H1
                var h1Node = htmlDoc.DocumentNode.SelectSingleNode("//h1");
                if (h1Node != null)
                {
                    var fullTitle = h1Node.InnerText.Trim();
                    if (fullTitle.Contains(" - "))
                    {
                        var parts = fullTitle.Split(" - ");
                        if (parts.Length > 1)
                        {
                            company.MaSoThue = parts[0].Trim();
                            company.TenCongTy = parts[1].Trim();
                        }
                    }
                    else
                    {
                        company.TenCongTy = fullTitle;
                        company.MaSoThue = taxCode;
                    }
                }
                else
                {
                    company.MaSoThue = taxCode;
                }

                // Lấy người đại diện từ link legalName
                var representativeLink = htmlDoc.DocumentNode.SelectSingleNode("//a[contains(@href, 'legalName')]");
                if (representativeLink != null)
                {
                    company.NguoiDaiDien = representativeLink.InnerText.Trim();
                }

                // Lấy các thông tin khác bằng cách tìm trong toàn bộ text - cải tiến
                var bodyText = htmlDoc.DocumentNode.SelectSingleNode("//body")?.InnerText ?? "";
                var lines = bodyText.Split('\n', '\r')
                    .Where(l => !string.IsNullOrWhiteSpace(l))
                    .Select(l => l.Trim())
                    .ToArray();

                // Tìm thông tin chính xác hơn
                for (int i = 0; i < lines.Length; i++)
                {
                    var line = lines[i];

                    // Địa chỉ
                    if (line.Equals("Địa chỉ", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                    {
                        var nextLine = lines[i + 1].Trim();
                        if (nextLine.Length > 10 && !nextLine.Contains("Ngày") && !nextLine.Contains("Quản lý") &&
                            !nextLine.Contains("Loại hình") && !nextLine.Contains("Tình trạng"))
                        {
                            company.DiaChi = nextLine;
                        }
                    }
                    // Ngày hoạt động
                    else if (line.Equals("Ngày hoạt động", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                    {
                        var nextLine = lines[i + 1].Trim();
                        if (nextLine.Length > 4 && (nextLine.Contains("-") || nextLine.Contains("/")) &&
                            !nextLine.Contains("Quản lý") && !nextLine.Contains("Loại hình"))
                        {
                            company.NgayHoatDong = nextLine;
                        }
                    }
                    // Quản lý bởi
                    else if (line.Equals("Quản lý bởi", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                    {
                        var nextLine = lines[i + 1].Trim();
                        if (nextLine.Length > 5 && !nextLine.Contains("Loại hình") && !nextLine.Contains("Tình trạng") &&
                            !nextLine.Contains("Ngày"))
                        {
                            company.QuanLyBoi = nextLine;
                        }
                    }
                    // Loại hình DN
                    else if (line.Equals("Loại hình DN", StringComparison.OrdinalIgnoreCase) && i + 1 < lines.Length)
                    {
                        var nextLine = lines[i + 1].Trim();
                        if (nextLine.Length > 5 && !nextLine.Contains("Tình trạng") && !nextLine.Contains("Ngày") &&
                            !nextLine.Contains("Quản lý"))
                        {
                            company.LoaiHinhDN = nextLine;
                        }
                    }
                    // Tình trạng - SỬA LẠI LOGIC ĐỂ LẤY ĐÚNG
                    else if (line.StartsWith("Tình trạng", StringComparison.OrdinalIgnoreCase))
                    {
                        // Tìm tình trạng trong cùng dòng (format: "Tình trạngĐang hoạt động...")
                        var tinhTrangText = line.Replace("Tình trạng", "").Trim();
                        if (!string.IsNullOrEmpty(tinhTrangText) && tinhTrangText.Length > 5)
                        {
                            company.TinhTrang = tinhTrangText;
                        }
                        // Nếu không có trong cùng dòng, lấy dòng tiếp theo
                        else if (i + 1 < lines.Length)
                        {
                            var nextLine = lines[i + 1].Trim();
                            if (nextLine.Length > 5 && !nextLine.Contains("Ngày") && !nextLine.Contains("Quản lý") &&
                                !nextLine.Contains("Loại hình"))
                            {
                                company.TinhTrang = nextLine;
                            }
                        }
                    }
                }

                // Fallback logic
                if (string.IsNullOrEmpty(company.LoaiHinhDN))
                {
                    // Ưu tiên thông tin từ tên công ty trước (chính xác hơn)
                    if (!string.IsNullOrEmpty(company.TenCongTy))
                    {
                        if (company.TenCongTy.Contains("HỘ KINH DOANH"))
                            company.LoaiHinhDN = "Hộ kinh doanh cá thể";
                        else if (company.TenCongTy.Contains("TNHH"))
                            company.LoaiHinhDN = "Công ty TNHH";
                        else if (company.TenCongTy.Contains("Cổ phần"))
                            company.LoaiHinhDN = "Công ty cổ phần";
                        else if (company.TenCongTy.Contains("Tư nhân"))
                            company.LoaiHinhDN = "Doanh nghiệp tư nhân";
                    }
                }

                // Fallback cho tình trạng nếu vẫn trống
                if (string.IsNullOrEmpty(company.TinhTrang))
                {
                    foreach (var line in lines)
                    {
                        if (line.Contains("Đang hoạt động") || line.Contains("Ngừng hoạt động") ||
                            line.Contains("Tạm ngừng") || line.Contains("Giải thể"))
                        {
                            company.TinhTrang = line;
                            break;
                        }
                    }
                }

                return company;
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi tra cứu mã số thuế {taxCode}: {ex.Message}");
            }
        }

        // Method đọc mã số thuế từ file
        private async Task<List<string>> ReadTaxCodesFromFile(string filePath)
        {
            var taxCodes = new List<string>();
            var extension = Path.GetExtension(filePath).ToLower();

            try
            {
                switch (extension)
                {
                    case ".txt":
                        var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
                        foreach (var line in lines)
                        {
                            var taxCode = line.Trim();
                            if (!string.IsNullOrWhiteSpace(taxCode))
                            {
                                taxCodes.Add(taxCode);
                            }
                        }
                        break;

                    case ".xls":
                    case ".xlsx":
                        // Đọc file Excel bằng EPPlus
                        using (var package = new OfficeOpenXml.ExcelPackage(new FileInfo(filePath)))
                        {
                            var worksheet = package.Workbook.Worksheets[0]; // Lấy sheet đầu tiên
                            if (worksheet != null)
                            {
                                var rowCount = worksheet.Dimension?.Rows ?? 0;
                                for (int row = 1; row <= rowCount; row++)
                                {
                                    var cellValue = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                                    if (!string.IsNullOrWhiteSpace(cellValue))
                                    {
                                        taxCodes.Add(cellValue);
                                    }
                                }
                            }
                        }
                        break;

                    default:
                        throw new Exception("Định dạng file không được hỗ trợ! Chỉ hỗ trợ .txt, .xls, .xlsx");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi đọc file: {ex.Message}");
            }

            return taxCodes;
        }
    }
}
